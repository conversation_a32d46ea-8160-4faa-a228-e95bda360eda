/************************************************************************
 *	Filename:		DEVDATABLOCKBUILDER.CPP
 *	Copyright:		Shanghai Baosight Software Co., Ltd.
 *
 *	Description:	$(Desp)���ݵ�������Ҫ�����ݿ���Ϣ .
 *
 *	@author:		baoyuansong
 *	@version		3/1/2013	baoyuansong	Initial Version
*************************************************************************/
#include "common/TagInfoArea.h"
#include "TagDataBlockBuilder.h"
#include "DataBlock.h"
#include "Device.h"
#include <string.h>
#include "gettext/libintl.h"
#include "MainTask.h"
#include "drvframework.h"
//#define _(STRING) gettext(STRING)
#define _(STRING) STRING
extern CCVLog g_CVDrvierCommonLog;
extern std::map<long, string> g_mapAllTags;
extern std::map<int32, ConfigErrTags > g_mapConfigErrTags;

bool IsTagInBlock(char szAddr[TAG_TOTAL_SEGMENT][ICV_IOADDR_MAXLEN], CVDATABLOCK * pcvDataBlock, int32&nBitOffSet)
{
	bool bRet = false;
	char* pSegAddr = szAddr[SECOND_SEGMENT];
	char* pSegStart = pSegAddr;
	char* pSegEnd = pSegAddr;
	char* pSegBitOffset = szAddr[THIRD_SEGMENT];
	char szTemp[ICV_IOADDR_MAXLEN];
	int32 nPlatNo;

	nBitOffSet = 0;
	if(pcvDataBlock == NULL)
	{
		return false;
	}
	if(pcvDataBlock->pszAddress == NULL || pcvDataBlock->pszName == NULL)
	{
		return false;
	}
	memset(szTemp, 0, ICV_IOADDR_MAXLEN);

	if(strlen(szAddr[SECOND_SEGMENT]) != 0) //
	{
		while(*pSegEnd != '\0')
		{
			if(*pSegEnd == '@')
			{
				memset(szTemp, 0, ICV_IOADDR_MAXLEN);
				memcpy(szTemp, pSegStart, pSegEnd - pSegStart);
				nPlatNo = atoi(szTemp);
				pSegStart = pSegEnd + 1;
			}
			pSegEnd ++;
		}

		if(strcmp(pSegStart, pcvDataBlock->pszName) == 0)
		{
			char *pszDigitalOffset = szAddr[THIRD_SEGMENT];	// ��ַƫ��

			// ��ȡ��ַ�����ݿ��ڵ�Byteƫ���������ֻ�����ݿ����ƶ�û�������ƫ�ƣ���ȡ��ʼ��ַ
			if (szAddr[THIRD_SEGMENT][0] == '\0') // ֻ�����ݿ飬�����ֽ�
			{
				nBitOffSet = 0;
				bRet = true;
			}
			else // �����ֽں�λ��ƫ����
			{
				int nAbsAddressBits = BITS_PER_BYTE;
				bool bUseAbsAddress = false;
				int nByteCount = 1;	// ÿ���ֽ�����D��DW��B
				// ��ַ��Ϊ ���ֿ�ͷ(��ʾ1���ֽڣ���D��ͷ��DW��ͷ\b\B
				if(szAddr[THIRD_SEGMENT][0] == 'B')
				{
					nAbsAddressBits = BITS_PER_BYTE;
					pszDigitalOffset = szAddr[THIRD_SEGMENT] + 1;
					bUseAbsAddress = true;
				}
				else if(szAddr[THIRD_SEGMENT][0] == 'W')
				{
					nAbsAddressBits = BITS_PER_BYTE * 2;
					pszDigitalOffset = szAddr[THIRD_SEGMENT] + 1;
					bUseAbsAddress = true;
				}
				else if(strlen(szAddr[THIRD_SEGMENT]) >= 2 && szAddr[THIRD_SEGMENT][0] == 'D' && szAddr[THIRD_SEGMENT][1] == 'W')
				{
					nAbsAddressBits = BITS_PER_BYTE * 4;	// DWORD ��32λ
					pszDigitalOffset = szAddr[THIRD_SEGMENT] + 2;
					bUseAbsAddress = true;
				}
				else if(strlen(szAddr[THIRD_SEGMENT]) >= 2 && szAddr[THIRD_SEGMENT][0] == 'F' && szAddr[THIRD_SEGMENT][1] == 'W')
				{
					nAbsAddressBits = BITS_PER_BYTE * 8;	// DWORD ��64λ
					pszDigitalOffset = szAddr[THIRD_SEGMENT] + 2;
					bUseAbsAddress = true;
				}
				else if(szAddr[THIRD_SEGMENT][0] == 'b') // bit
				{
					nAbsAddressBits = 1;
					pszDigitalOffset = szAddr[THIRD_SEGMENT] + 1;
					bUseAbsAddress = true;
				}

				// ����������λΪ��λ��ƫ������
				if(bUseAbsAddress) // ʹ��B��b��W��DW����
				{
					nBitOffSet = ::atoi(pszDigitalOffset) * nAbsAddressBits;
				}
				else // ���ڿ��豸��dataBlockCfg.nStartWord ���ܲ��� 0 ������Ϊ0
				{
					// ��ȡ��ַ�����ݿ��ڵ�Byteƫ����
					int nTagAddress = ::atoi(pszDigitalOffset); // such as: 400003
					if(nTagAddress > 99999) // �������ĵ�ַֻ��Ϊ99999���ֽڡ���499999�����Ϊ99999
					{
						nTagAddress = nTagAddress - nTagAddress / 100000 * 100000;
						int nStartAddress = ::atoi(pcvDataBlock->pszAddress);
						if(nStartAddress > 99999) // �������ĵ�ַֻ��Ϊ99999���ֽڡ���499999�����Ϊ99999
							nStartAddress = nStartAddress - nStartAddress / 100000 * 100000;
						nBitOffSet = (nTagAddress - nStartAddress) *  nByteCount * pcvDataBlock->nElemBits;
					}
					else
						nBitOffSet = nTagAddress *  nByteCount * pcvDataBlock->nElemBits;
				}


				// ����������λΪ��λ��ƫ���������ڿ��豸��dataBlockCfg.nStartWord ���ܲ��� 0 ������Ϊ0
				if (szAddr[FOURTH_SEGMENT][0] != '\0') // ��������������ң��ź������ֽ��ڵ�ƫ�Ƶ�ַ
					nBitOffSet += ::atoi(szAddr[FOURTH_SEGMENT]);	// �������ð�ź����ƫ�ƣ���ҲҪ����

				if(nBitOffSet < 0)
				{
					CV_WARN(g_CVDrvierCommonLog, -1, "TagAddr[%s %s %s %s] invalid bitoffset.",szAddr[DEVICE_SEGMENT], szAddr[SECOND_SEGMENT], szAddr[THIRD_SEGMENT], szAddr[FOURTH_SEGMENT]);
					return false;
				}

				// �����ֽں�λƫ��
				int32 nByteOffset = 0;
				nByteOffset = nBitOffSet / BITS_PER_BYTE;
				// dataBlockCfg.nWordBytes; // ������ÿ��Ԫ�ص�λ����ͨ��Ϊ8��Ҳ����Ϊ1����16��32
				int nDbDataLenInBytes = (int)pcvDataBlock->nBlockDataSize;	// 
				if (nByteOffset < 0 || nByteOffset >= nDbDataLenInBytes)
				{
					CV_WARN(g_CVDrvierCommonLog,-1,"TagAddr[%s %s %s %s] exceed blockLength %d.",szAddr[DEVICE_SEGMENT],szAddr[SECOND_SEGMENT],szAddr[THIRD_SEGMENT],szAddr[FOURTH_SEGMENT],nDbDataLenInBytes);
					return false;
				}
				bRet = true;
			}	
		}
	}
	return bRet;
}
int32 ParseIoAddr(char* szIoAddr,  char szAddr[TAG_TOTAL_SEGMENT][ICV_IOADDR_MAXLEN], int32 & nLength)
{
	const char chDelim = ':';	
	int i = 0;

	memset(szAddr, 0, TAG_TOTAL_SEGMENT * ICV_IOADDR_MAXLEN);

	// ��szAddressInfo��":"Ϊ�ָ������в��
	// "::"����Ϊ":"
	char *pAddrBuf = (char *)szIoAddr;
	char *pSegStart = (char *)szIoAddr; // ÿһС�ڵ���ʼ��ַ
	char *pSegEnd = (char *)szIoAddr;   // ÿһС�ڵĽ�����ַ
	int nSegNo = 0; // С�ڵĸ���
	while (*pSegEnd != '\0')
	{
		if(*pSegEnd != chDelim)  // ����ð�Ž�����
		{
			pSegEnd ++;
			continue;
		}

		// ��ð�Ž�����
		int nSegLen = pSegEnd - pSegStart;
		if(nSegLen >= ICV_IOADDR_MAXLEN)
			return EC_ICV_DA_INVALID_IO_ADDR;

		memcpy(szAddr[nSegNo], pSegStart, nSegLen);
		nSegNo ++;
		pSegEnd ++; // ָ��ð�ŵ���һ���ط��ַ�
		pSegStart = pSegEnd;
		if(nSegNo >= TAG_TOTAL_SEGMENT)//��ʱ,nSegNoӦ��С��4��
		{
			CV_ERROR(g_CVDrvierCommonLog, EC_ICV_DA_ADDRESS_SEGNUM_TOOMUCH, "ParseIoAddr : %s, failed, nSegNo >= 4", szIoAddr);
			return EC_ICV_DA_ADDRESS_SEGNUM_TOOMUCH;
		}
	}

	// �����ʣ����0��β�����һ��
	if(*pSegStart != '\0' && *pSegStart != *pSegEnd)
	{
		memcpy(szAddr[nSegNo], pSegStart, pSegEnd - pSegStart);
		nSegNo ++;
	}

	if(nSegNo == 0 || nSegNo == 1)
	{
		CV_ERROR(g_CVDrvierCommonLog, EC_ICV_DA_ADDRESS_SEGNUM_TOOSMALL, "ParseIoAddr : %s, failed, nSegNo == 0 || nSegNo == 1", szIoAddr);
		return EC_ICV_DA_ADDRESS_SEGNUM_TOOSMALL;
	}
	if(nSegNo > TAG_TOTAL_SEGMENT) //��ʱ,nSegNo����Ϊ4��
	{
		CV_ERROR(g_CVDrvierCommonLog, EC_ICV_DA_ADDRESS_SEGNUM_TOOMUCH, "ParseIoAddr : %s, failed, nSegNo > 4", szIoAddr);
		return EC_ICV_DA_ADDRESS_SEGNUM_TOOMUCH;
	}

	// ���һ�ε�ַ���ܰ�����#�š������ǵ����λ���Ķ�
	if(szAddr[nSegNo - 1][0] != '\0')
	{
		pSegEnd = szAddr[nSegNo - 1];
		while(*pSegEnd != '\0' && *pSegEnd != '#')
			pSegEnd ++;

		// �ҵ���#��
		if(*pSegEnd == '#')
		{
			nLength = ::atoi(pSegEnd + 1);
			*pSegEnd = '\0';
			if(nLength <= 0)
				return EC_ICV_DA_IOADDRESS_DATALENGTH_TOOSMALL;
			//��Ϊ���֧��2048���ֽڣ���ʹ��cvdriverdda���������ó��ȳ���2048ʱ��ʾ���ȹ�����
			if(nLength > 2048)
				return EC_ICV_DA_IOADDRESS_DATALENGTH_TOOMUCH;
		}
	}

	return ICV_SUCCESS;
}

CTagDataBlockBuilder::CTagDataBlockBuilder(const char *pDrvName, CDevice *pDevice)
{
	m_pTagReader = NULL;
	m_pDevice = pDevice;
	if (NULL != pDrvName && NULL != pDevice)
	{
		m_pTagReader = new CTagReader(pDrvName, pDevice->m_strName.c_str());
	}
}

CTagDataBlockBuilder::~CTagDataBlockBuilder(void)
{
	delete m_pTagReader;
	m_pTagReader = NULL;
}


void CTagDataBlockBuilder::ReadTags(TagInfoVector &vecTagInfo)
{	//��ȡ����tag��Ϣ
	if(NULL != m_pTagReader)
		m_pTagReader->ReadTags(vecTagInfo);
}
/**
 *  $(Desp) ����tag�����ɶ�Ӧ�Ŀ���Ϣ.
 *  $(Detail) .
 *
 *  @return		void.
 *
 *  @version	3/1/2013  baoyuansong  Initial Version.
 */
void CTagDataBlockBuilder::MakeTagDataBlocks(TagInfoVector &vecTagInfo)
{
	//����tag��Ϣ��ȡ����tag����
	TagGroupInfoVector vecTagGrpInfo;
	TagInfoVector vecOutTagInfo;
	TagInfoMultiMap multiMapOutTagInfo;
	GetTagGrps(vecTagInfo, vecOutTagInfo, vecTagGrpInfo);
	if (vecOutTagInfo.empty() || vecTagGrpInfo.empty())
	{
		CV_WARN(g_CVDrvierCommonLog, -1, "No output Tags or Groups. %d %d", vecOutTagInfo.size(), vecTagGrpInfo.size());
		//return;
	}

	//����multimap���������������
	for (TagInfoVector::const_iterator iter = vecOutTagInfo.begin(); iter != vecOutTagInfo.end(); ++iter)
	{
		TagInfo tag;
		strcpy(tag.szTagName, iter->szTagName);
		strcpy(tag.szGrpName, iter->szGrpName);
		strcpy(tag.szAddress, iter->szAddress);
		tag.nTagType = iter->nTagType;
		tag.nTagID = iter->nTagID;
		tag.nPollRate = iter->nPollRate;
		tag.nMDIAddrNo = iter->nMDIAddrNo;
		tag.nDataType = iter->nDataType;
		tag.nBitOffSet = iter->nBitOffSet;
		multiMapOutTagInfo.insert(std::make_pair(iter->szGrpName, tag));
	}

 	//����tag��Ϣ���飬�����������ݿ�
 	for (TagGroupInfoVector::iterator iter = vecTagGrpInfo.begin(); iter != vecTagGrpInfo.end(); ++iter)
 	{
 		CDataBlock *pDataBlock = new CDataBlock(m_pDevice);
 		pDataBlock->m_strName = iter->szGroupName;
 		pDataBlock->m_strAddress = iter->szAddress;
 		pDataBlock->m_nElemNum = iter->nElemNum;
 		pDataBlock->m_nElemBits = iter->nElemBits;
 		pDataBlock->m_strBlockType = iter->szGroupType;
 		pDataBlock->m_nPollRate = iter->nPollRate;
 		pDataBlock->m_strParam1 = iter->szParam1;
 		pDataBlock->m_strParam2 = iter->szParam2;
 		pDataBlock->m_strParam3 = iter->szParam3;
 		pDataBlock->m_nPollPhase = 0;
 		
 		if ((pDataBlock->m_nElemNum * pDataBlock->m_nElemBits)%BITS_PER_BYTE == 0)
 			pDataBlock->m_nBlockSize = (pDataBlock->m_nElemNum * pDataBlock->m_nElemBits)/BITS_PER_BYTE;	// ȱʡΪ����
 		else
 			pDataBlock->m_nBlockSize = (pDataBlock->m_nElemNum * pDataBlock->m_nElemBits)/BITS_PER_BYTE + 1;	// ȱʡΪ����

		pDataBlock->CreateBlockBuffer();

 		m_pDevice->m_mapDataBlocks[pDataBlock->m_strName] = pDataBlock;

		if(g_pfnOnDataBlockAdd)
			g_pfnOnDataBlockAdd(m_pDevice, pDataBlock);

		std::pair <TagInfoMultiMap::const_iterator, TagInfoMultiMap::const_iterator > ret = 
			multiMapOutTagInfo.equal_range(pDataBlock->m_strName.c_str());
		if (ret.first == ret.second)
		{
			CV_WARN(g_CVDrvierCommonLog,-1,"Block %s has no tags", pDataBlock->m_strName.c_str());
		}
		for(TagInfoMultiMap::const_iterator iterTag = ret.first; iterTag != ret.second; ++iterTag)
		{
			TagAddrID *tagaddrid = new TagAddrID;
			int32 nLength;
			int32 nRet = ICV_SUCCESS;
			tagaddrid->nTagID = iterTag->second.nTagID;
			nRet = GetBitLenByDataType(iterTag->second.nDataType, (char*)iterTag->second.szAddress, nLength);
			if (nRet != ICV_SUCCESS || nLength == 0)
			{
				CV_ERROR(g_CVDrvierCommonLog,nRet,"Block %s has invalid TagName %s TagAddr %s", pDataBlock->m_strName.c_str(),iterTag->second.szTagName,iterTag->second.szAddress);
				continue;
			}
			tagaddrid->nBitLen = nLength;
			tagaddrid->nBitOffset = iterTag->second.nBitOffSet;
			tagaddrid->nDataType = iterTag->second.nDataType;
			tagaddrid->nMDIAddrNo = iterTag->second.nMDIAddrNo;

			g_mapAllTags.insert(make_pair(iterTag->second.nTagID, iterTag->second.szTagName));
			pDataBlock->m_vecTagAddrID.push_back(tagaddrid);
			CV_DEBUG(g_CVDrvierCommonLog,"Block %s add Tag %s %d %s", pDataBlock->m_strName.c_str(), iterTag->second.szTagName, tagaddrid->nTagID, iterTag->second.szAddress);
			g_mapConfigErrTags[iterTag->second.nTagID].bError = false;

			TagBlockAddr* pTagBlockAddr = new TagBlockAddr;
			if(iterTag->second.nTagType != ICV_TAGTYPE_MDI)
			{
				pTagBlockAddr->strDeviceName = pDataBlock->m_pDevice->m_strName;
				pTagBlockAddr->strDataBlockName = pDataBlock->m_strName;
				pTagBlockAddr->strTagAddr = iterTag->second.szAddress;
				pTagBlockAddr->nBitLen = nLength;
				pTagBlockAddr->nBitOffset = iterTag->second.nBitOffSet;
				pTagBlockAddr->nDataType = iterTag->second.nDataType;
				pTagBlockAddr->nTagType = iterTag->second.nTagType;
			}
			else
			{
				if(iterTag->second.nMDIAddrNo == 0)
				{
					pTagBlockAddr->strDeviceName = pDataBlock->m_pDevice->m_strName;
					pTagBlockAddr->strDataBlockName = pDataBlock->m_strName;
					pTagBlockAddr->strTagAddr = iterTag->second.szAddress;
					pTagBlockAddr->nBitLen = nLength;
					pTagBlockAddr->nBitOffset = iterTag->second.nBitOffSet;
					pTagBlockAddr->nDataType = iterTag->second.nDataType;
					pTagBlockAddr->nTagType = iterTag->second.nTagType;
				}
				else if(iterTag->second.nMDIAddrNo == 1)
				{
					pTagBlockAddr->strDeviceName1 = pDataBlock->m_pDevice->m_strName;
					pTagBlockAddr->strDataBlockName1 = pDataBlock->m_strName;
					pTagBlockAddr->strTagAddr1 = iterTag->second.szAddress;
					pTagBlockAddr->nBitLen1 = nLength;
					pTagBlockAddr->nBitOffset1 = iterTag->second.nBitOffSet;
					pTagBlockAddr->nDataType = iterTag->second.nDataType;
					pTagBlockAddr->nTagType = iterTag->second.nTagType;
				}
				else if(iterTag->second.nMDIAddrNo == 2)
				{
					pTagBlockAddr->strDeviceName2 = pDataBlock->m_pDevice->m_strName;
					pTagBlockAddr->strDataBlockName2 = pDataBlock->m_strName;
					pTagBlockAddr->strTagAddr2 = iterTag->second.szAddress;
					pTagBlockAddr->nBitLen2 = nLength;
					pTagBlockAddr->nBitOffset2 = iterTag->second.nBitOffSet;
					pTagBlockAddr->nDataType = iterTag->second.nDataType;
					pTagBlockAddr->nTagType = iterTag->second.nTagType;
				}
			}
			MAIN_TASK->AddTag2Map(tagaddrid->nTagID, pTagBlockAddr, iterTag->second.nMDIAddrNo);			
		}
 	}
}

void CTagDataBlockBuilder::MakeTagDataBlocks(TagInfoVector &vecTagInfo,CDevice* pDevice, unsigned int nCurrConnIndex, unsigned int nTotalConNum)
{
	//����tag��Ϣ��ȡ����tag����
	TagGroupInfoVector vecTagGrpInfo;
	TagInfoVector vecOutTagInfo;
	TagInfoMultiMap multiMapOutTagInfo;
	GetTagGrps(vecTagInfo, vecOutTagInfo, vecTagGrpInfo, pDevice, nCurrConnIndex, nTotalConNum);
	if (vecOutTagInfo.empty() || vecTagGrpInfo.empty())
	{
		CV_WARN(g_CVDrvierCommonLog,-1,"No output Tags or Groups. %d %d",vecOutTagInfo.size(),vecTagGrpInfo.size());
		//return;
	}

	//����multimap���������������
	for (TagInfoVector::const_iterator iter = vecOutTagInfo.begin(); iter != vecOutTagInfo.end(); ++iter)
	{
		TagInfo tag;
		strcpy(tag.szTagName, iter->szTagName);
		strcpy(tag.szGrpName, iter->szGrpName);
		strcpy(tag.szAddress, iter->szAddress);
		tag.nTagType = iter->nTagType;
		tag.nTagID = iter->nTagID;
		tag.nPollRate = iter->nPollRate;
		tag.nMDIAddrNo = iter->nMDIAddrNo;
		tag.nDataType = iter->nDataType;
		tag.nBitOffSet = iter->nBitOffSet;
		multiMapOutTagInfo.insert(std::make_pair(iter->szGrpName, tag));
	}

	//����tag��Ϣ���飬�����������ݿ�
	for (TagGroupInfoVector::iterator iter = vecTagGrpInfo.begin(); iter != vecTagGrpInfo.end(); ++iter)
	{
		CDataBlock *pDataBlock = new CDataBlock(m_pDevice);
		pDataBlock->m_strName = iter->szGroupName;
		pDataBlock->m_strAddress = iter->szAddress;
		pDataBlock->m_nElemNum = iter->nElemNum;
		pDataBlock->m_nElemBits = iter->nElemBits;
		pDataBlock->m_strBlockType = iter->szGroupType;
		pDataBlock->m_nPollRate = iter->nPollRate;
		pDataBlock->m_strParam1 = iter->szParam1;
		pDataBlock->m_strParam2 = iter->szParam2;
		pDataBlock->m_strParam3 = iter->szParam3;
		pDataBlock->m_nPollPhase = 0;

		if ((pDataBlock->m_nElemNum * pDataBlock->m_nElemBits)%BITS_PER_BYTE == 0)
			pDataBlock->m_nBlockSize = (pDataBlock->m_nElemNum * pDataBlock->m_nElemBits)/BITS_PER_BYTE;	// ȱʡΪ����
		else
			pDataBlock->m_nBlockSize = (pDataBlock->m_nElemNum * pDataBlock->m_nElemBits)/BITS_PER_BYTE + 1;	// ȱʡΪ����

		pDataBlock->CreateBlockBuffer();

		m_pDevice->m_mapDataBlocks[pDataBlock->m_strName] = pDataBlock;

		if(g_pfnOnDataBlockAdd)
			g_pfnOnDataBlockAdd(m_pDevice, pDataBlock);

		std::pair <TagInfoMultiMap::const_iterator, TagInfoMultiMap::const_iterator > ret =
			multiMapOutTagInfo.equal_range(pDataBlock->m_strName.c_str());
		if (ret.first == ret.second)
		{
			CV_WARN(g_CVDrvierCommonLog, -1, "Block %s has no tags", pDataBlock->m_strName.c_str());
		}
		for (TagInfoMultiMap::const_iterator iterTag = ret.first; iterTag != ret.second; ++iterTag)
		{
			TagAddrID* tagaddrid = new TagAddrID;
			int32 nLength;
			int32 nRet = ICV_SUCCESS;
			tagaddrid->nTagID = iterTag->second.nTagID;
			nRet = GetBitLenByDataType(iterTag->second.nDataType, (char*)iterTag->second.szAddress, nLength);
			if (nRet != ICV_SUCCESS || nLength == 0)
			{
				CV_ERROR(g_CVDrvierCommonLog, nRet, "Invalid TagName %s TagAddr %s", iterTag->second.szTagName, iterTag->second.szAddress);
				continue;
			}
			tagaddrid->nBitLen = nLength;
			tagaddrid->nBitOffset = iterTag->second.nBitOffSet;
			tagaddrid->nDataType = iterTag->second.nDataType;
			tagaddrid->nMDIAddrNo = iterTag->second.nMDIAddrNo;

			g_mapAllTags.insert(make_pair(iterTag->second.nTagID, iterTag->second.szTagName));
			pDataBlock->m_vecTagAddrID.push_back(tagaddrid);
			CV_DEBUG(g_CVDrvierCommonLog, "Block %s add Tag %s %d %s", pDataBlock->m_strName.c_str(), iterTag->second.szTagName, tagaddrid->nTagID, iterTag->second.szAddress);
			g_mapConfigErrTags[iterTag->second.nTagID].bError = false;

			TagBlockAddr* pTagBlockAddr = new TagBlockAddr;
			if (iterTag->second.nTagType != ICV_TAGTYPE_MDI)
			{
				pTagBlockAddr->strDeviceName = pDataBlock->m_pDevice->m_strName;
				pTagBlockAddr->strDataBlockName = pDataBlock->m_strName;
				pTagBlockAddr->strTagAddr = iterTag->second.szAddress;
				pTagBlockAddr->nBitLen = nLength;
				pTagBlockAddr->nBitOffset = iterTag->second.nBitOffSet;
				pTagBlockAddr->nDataType = iterTag->second.nDataType;
				pTagBlockAddr->nTagType = iterTag->second.nTagType;
			}
			else
			{
				if (iterTag->second.nMDIAddrNo == 0)
				{
					pTagBlockAddr->strDeviceName = pDataBlock->m_pDevice->m_strName;
					pTagBlockAddr->strDataBlockName = pDataBlock->m_strName;
					pTagBlockAddr->strTagAddr = iterTag->second.szAddress;
					pTagBlockAddr->nBitLen = nLength;
					pTagBlockAddr->nBitOffset = iterTag->second.nBitOffSet;
					pTagBlockAddr->nDataType = iterTag->second.nDataType;
					pTagBlockAddr->nTagType = iterTag->second.nTagType;
				}
				else if (iterTag->second.nMDIAddrNo == 1)
				{
					pTagBlockAddr->strDeviceName1 = pDataBlock->m_pDevice->m_strName;
					pTagBlockAddr->strDataBlockName1 = pDataBlock->m_strName;
					pTagBlockAddr->strTagAddr1 = iterTag->second.szAddress;
					pTagBlockAddr->nBitLen1 = nLength;
					pTagBlockAddr->nBitOffset1 = iterTag->second.nBitOffSet;
					pTagBlockAddr->nDataType = iterTag->second.nDataType;
					pTagBlockAddr->nTagType = iterTag->second.nTagType;
				}
				else if (iterTag->second.nMDIAddrNo == 2)
				{
					pTagBlockAddr->strDeviceName2 = pDataBlock->m_pDevice->m_strName;
					pTagBlockAddr->strDataBlockName2 = pDataBlock->m_strName;
					pTagBlockAddr->strTagAddr2 = iterTag->second.szAddress;
					pTagBlockAddr->nBitLen2 = nLength;
					pTagBlockAddr->nBitOffset2 = iterTag->second.nBitOffSet;
					pTagBlockAddr->nDataType = iterTag->second.nDataType;
					pTagBlockAddr->nTagType = iterTag->second.nTagType;
				}
			}
			MAIN_TASK->AddTag2Map(tagaddrid->nTagID, pTagBlockAddr, iterTag->second.nMDIAddrNo);
		}
	}
}

int32 CTagDataBlockBuilder::GetBitLenByDataType(uint8 nDataType, char* szIoAddr, int32 & nLength)
{
	int32 nRet = ICV_SUCCESS;
	nLength = 0;
	switch(nDataType)
	{
	case DT_BIT:
		nLength = 1;
		break;
	case DT_CHAR:
		nLength = sizeof(char)*BITS_PER_BYTE;
		break;

	case DT_UCHAR:
		nLength = sizeof(unsigned char)*BITS_PER_BYTE;
		break;

	case DT_SINT16:
		nLength = sizeof(int16)*BITS_PER_BYTE;
		break;

	case DT_UINT16:
		nLength = sizeof(uint16)*BITS_PER_BYTE;
		break;

	case DT_SLONG:
	case DT_TIME:
	case DT_TOD:
	case DT_DT:
		nLength = sizeof(int32)*BITS_PER_BYTE;
		break;

	case DT_ULONG:
	case DT_DATE:
		nLength = sizeof(uint32)*BITS_PER_BYTE;
		break;

	case DT_FLT:
		nLength = sizeof(float32)*BITS_PER_BYTE;
		break;

	case DT_DBL:
		nLength = sizeof(float64)*BITS_PER_BYTE;
		break;

	case DT_INT64:
	case DT_LTIME:
		nLength = sizeof(int64)*BITS_PER_BYTE;
		break;

	case DT_UINT64:
		nLength = sizeof(uint64)*BITS_PER_BYTE;
		break;
	case DT_ASCII:
		{
			char szAddr[TAG_TOTAL_SEGMENT][ICV_IOADDR_MAXLEN];
			if(g_pfnParseIoAddr == NULL)
			{
				nRet = ParseIoAddr(szIoAddr, szAddr, nLength);
			}
			else
			{
				nRet = g_pfnParseIoAddr(szIoAddr, szAddr, nLength);
			}
			
	
			if(nRet != ICV_SUCCESS)
			{
				return nRet;
			}
			if(nLength == 0)
			{
				nLength = DEFAULT_TXT_BLOB_LEN*BITS_PER_BYTE;
			}
			else
			{
				nLength = nLength*BITS_PER_BYTE;
			}
		}
		break;
	case DT_BLOB:
		{
			char szAddr[TAG_TOTAL_SEGMENT][ICV_IOADDR_MAXLEN];
			if(g_pfnParseIoAddr == NULL)
			{
				nRet = ParseIoAddr(szIoAddr, szAddr, nLength);
			}
			else
			{
				nRet = g_pfnParseIoAddr(szIoAddr, szAddr, nLength);
			}
			if(nRet != ICV_SUCCESS)
			{
				return nRet;
			}
			if(nLength == 0)
			{
				nLength = DEFAULT_TXT_BLOB_LEN*BITS_PER_BYTE;
			}
			else
			{
				nLength = nLength*BITS_PER_BYTE;
			}
		}
		break;
	case DT_BYTE:
		nLength = sizeof(char)*BITS_PER_BYTE;
		break;
	case DT_WORLD:
		nLength = sizeof(uint16)*BITS_PER_BYTE;
		break;
	case DT_DWORLD:
		nLength = sizeof(uint32)*BITS_PER_BYTE;
		break;
	case DT_LWORLD:
		nLength = sizeof(uint64)*BITS_PER_BYTE;
		break;
	case DT_CHAR_:
		nLength = sizeof(char)*BITS_PER_BYTE;
		break;
	case DT_UDT:
		{
			char szAddr[TAG_TOTAL_SEGMENT][ICV_IOADDR_MAXLEN];
			if(g_pfnParseIoAddr == NULL)
			{
				nRet = ParseIoAddr(szIoAddr, szAddr, nLength);
			}
			else
			{
				nRet = g_pfnParseIoAddr(szIoAddr, szAddr, nLength);
			}
	
			if(nRet != ICV_SUCCESS)
			{
				return nRet;
			}
			if(nLength == 0)
			{
				nLength = DEFAULT_UDT_LEN*BITS_PER_BYTE;
			}
			else
			{
				nLength = nLength*BITS_PER_BYTE;
			}
		}
		break;
	case CV_MAX_NUMBER_DATATYPE:
		break;
	}
	return ICV_SUCCESS;
}

int32 CTagDataBlockBuilder::ParseTagAndInsertBlock(TagInfoVector &vecTagInfo, CDataBlock *pDataBlock)
{
	int32 nRet = ICV_SUCCESS;
	if(pDataBlock == NULL)
	{
		return -1;
	}
	for(TagInfoVector::iterator iterTag = vecTagInfo.begin(); iterTag != vecTagInfo.end(); ++iterTag)
	{
		char szAddr[TAG_TOTAL_SEGMENT][ICV_IOADDR_MAXLEN];
		int32 nLength = 0;

		if(g_pfnParseIoAddr == NULL)
		{
			nRet = ParseIoAddr(iterTag->szAddress, szAddr, nLength);
		}
		else
		{
			nRet = g_pfnParseIoAddr(iterTag->szAddress, szAddr, nLength);
		}

		if(nRet != ICV_SUCCESS)
		{
			CV_INFO(g_CVDrvierCommonLog, "ParseIoAddr : %s, failed", iterTag->szAddress);
			continue;
		}
		if(iterTag->nDataType == DT_ASCII || iterTag->nDataType == DT_BLOB)
		{
			if(nLength == 0)
			{
				nLength = DEFAULT_TXT_BLOB_LEN * BITS_PER_BYTE;
			}
			else
			{
				nLength = nLength * BITS_PER_BYTE;
			}
		}
		else
		{
			GetBitLenByDataType(iterTag->nDataType, iterTag->szAddress, nLength);
		}
		if (szAddr[DEVICE_SEGMENT][0] == '\0')
		{
			CV_INFO(g_CVDrvierCommonLog, "addr : %s, szAddr[DEVICE_SEGMENT][0] NULL", iterTag->szAddress);
			continue;
		}

		if (szAddr[SECOND_SEGMENT][0] == '\0')
		{
			CV_INFO(g_CVDrvierCommonLog, "addr : %s, szAddr[SECOND_SEGMENT][0] NULL", iterTag->szAddress);
			continue;
		}
		int32  nBitOffSet = 0; 
		CVDATABLOCK * pDataBlockInfo = NULL;
		pDataBlockInfo = Drv_GetDataBlockInfo(pDataBlock);
		bool bRet;

		if(g_pfnIsTagInBlock == NULL)
		{
			bRet = IsTagInBlock(szAddr, pDataBlockInfo, nBitOffSet);
		}
		else
		{
			bRet = g_pfnIsTagInBlock(szAddr, pDataBlockInfo, nBitOffSet);
		}

		if(bRet == true)
		{
			TagAddrID *tagaddrid = new TagAddrID;
			int32 nRet = ICV_SUCCESS;
			tagaddrid->nTagID = iterTag->nTagID;
			tagaddrid->nBitLen = nLength;
			tagaddrid->nBitOffset = nBitOffSet;
			tagaddrid->nDataType = iterTag->nDataType;
			tagaddrid->nMDIAddrNo = iterTag->nMDIAddrNo;

			g_mapAllTags.insert(make_pair(iterTag->nTagID, iterTag->szTagName));

			pDataBlock->m_vecTagAddrID.push_back(tagaddrid);
			CV_DEBUG(g_CVDrvierCommonLog, "Block %s add Tag %s %d %s", pDataBlockInfo->pszName, iterTag->szTagName, tagaddrid->nTagID, iterTag->szAddress);
			g_mapConfigErrTags[iterTag->nTagID].bError = false;

			TagBlockAddr* pTagBlockAddr = new TagBlockAddr;
			if(iterTag->nTagType != ICV_TAGTYPE_MDI)
			{
				pTagBlockAddr->strDeviceName = pDataBlock->m_pDevice->m_strName;
				pTagBlockAddr->strDataBlockName = pDataBlock->m_strName;
				pTagBlockAddr->strTagAddr = iterTag->szAddress;
				pTagBlockAddr->nBitLen = nLength;
				pTagBlockAddr->nBitOffset = nBitOffSet;
				pTagBlockAddr->nDataType = iterTag->nDataType;
				pTagBlockAddr->nTagType = iterTag->nTagType;			
			}
			else
			{
				if(iterTag->nMDIAddrNo == 0)
				{
					pTagBlockAddr->strDeviceName = pDataBlock->m_pDevice->m_strName;
					pTagBlockAddr->strDataBlockName = pDataBlock->m_strName;
					pTagBlockAddr->strTagAddr = iterTag->szAddress;
					pTagBlockAddr->nBitLen = nLength;
					pTagBlockAddr->nBitOffset = nBitOffSet;
					pTagBlockAddr->nDataType = iterTag->nDataType;
					pTagBlockAddr->nTagType = iterTag->nTagType;
				}
				else if(iterTag->nMDIAddrNo == 1)
				{
					pTagBlockAddr->strDeviceName1 = pDataBlock->m_pDevice->m_strName;
					pTagBlockAddr->strDataBlockName1 = pDataBlock->m_strName;
					pTagBlockAddr->strTagAddr1 = iterTag->szAddress;
					pTagBlockAddr->nBitLen1 = nLength;
					pTagBlockAddr->nBitOffset1 = nBitOffSet;
					pTagBlockAddr->nDataType = iterTag->nDataType;
					pTagBlockAddr->nTagType = iterTag->nTagType;
				}
				else if(iterTag->nMDIAddrNo == 2)
				{
					pTagBlockAddr->strDeviceName2 = pDataBlock->m_pDevice->m_strName;
					pTagBlockAddr->strDataBlockName2 = pDataBlock->m_strName;
					pTagBlockAddr->strTagAddr2 = iterTag->szAddress;
					pTagBlockAddr->nBitLen2 = nLength;
					pTagBlockAddr->nBitOffset2 = nBitOffSet;
					pTagBlockAddr->nDataType = iterTag->nDataType;
					pTagBlockAddr->nTagType = iterTag->nTagType;
				}
			}
			CV_DEBUG(g_CVDrvierCommonLog, "AddTag ID : %d, block : %s", tagaddrid->nTagID, pDataBlock->m_strName.c_str());
			MAIN_TASK->AddTag2Map(tagaddrid->nTagID, pTagBlockAddr, iterTag->nMDIAddrNo);
		}
	}
	return nRet;
}

/**
 *  $(Desp)�����û�����dll�ӿڣ�������������͵����Ϣ�����ڵ��ûص��漰���ڴ�����ͷ����⣬�˴������己�������ݽṹ.
 *  $(Detail) .
 *
 *  @param		-[in,out]  DEV_TAG_INFO_VECTOR & vecTagInfo : ��PDSConfig���ݿ��ж�ȡ������tag����Ϣ
 *  @param		-[out]  DEV_TAG_INFO_VECTOR & vecOutTagInfo : �����������tag����Ϣ����ʱtag����Ϣ��Я���˷�����Ϣ
 *  @param		-[out]  TAG_GROUP_INFO_VECTOR & vecGrpInfo : ���صķ�����Ϣ
 *  @return		void.
 *
 *  @version	3/17/2013  baoyuansong  Initial Version.
 */
void CTagDataBlockBuilder::GetTagGrps( TagInfoVector &vecTagInfo, TagInfoVector &vecOutTagInfo, TagGroupInfoVector &vecGrpInfo )
{
	unsigned int nOutTagNum = vecTagInfo.size();
	unsigned int nGrpNum = vecTagInfo.size();
	vecOutTagInfo.resize(nOutTagNum);
	vecGrpInfo.resize(nGrpNum);
	//ʵ��tag����
	if (g_pfnTagsToGroups != NULL)
	{
        g_pfnTagsToGroups(&vecTagInfo[0], vecTagInfo.size(), &vecOutTagInfo[0], &nOutTagNum, &vecGrpInfo[0], &nGrpNum);
        if (nGrpNum < vecGrpInfo.size())
        {
            vecGrpInfo.resize(nGrpNum);
        }
    }
    else
    {
        vecGrpInfo.clear();
        vecOutTagInfo.clear();
    }
}

void CTagDataBlockBuilder::GetTagGrps( TagInfoVector &vecTagInfo, TagInfoVector &vecOutTagInfo, TagGroupInfoVector &vecGrpInfo, CDevice* pDevice, 
	unsigned int nCurrConnIndex, unsigned int nTotalConNum)
{
	unsigned int nOutTagNum = vecTagInfo.size();
	unsigned int nGrpNum = vecTagInfo.size();
	vecOutTagInfo.resize(nOutTagNum);
	vecGrpInfo.resize(nGrpNum);
	//ʵ��tag����
	if (g_pfnTagsToGroups != NULL)
	{
		g_pfnTagsToGroups(&vecTagInfo[0], vecTagInfo.size(), &vecOutTagInfo[0], &nOutTagNum, &vecGrpInfo[0], &nGrpNum);
		if (nGrpNum < vecGrpInfo.size())
		{
			vecGrpInfo.resize(nGrpNum);
		}
	}
	else if (g_pfnTagsToGroupsEx != NULL)
	{
		g_pfnTagsToGroupsEx(&vecTagInfo[0], vecTagInfo.size(), &vecOutTagInfo[0], &nOutTagNum, &vecGrpInfo[0], &nGrpNum, pDevice, nCurrConnIndex, nTotalConNum);
		if (nGrpNum < vecGrpInfo.size())
		{
			vecGrpInfo.resize(nGrpNum);
		}
	}
	else
	{
		vecGrpInfo.clear();
		vecOutTagInfo.clear();
	}
}
