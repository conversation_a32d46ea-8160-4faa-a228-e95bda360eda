/************************************************************************
 *	Filename:		CVDRIVERCOMMON.CPP
 *	Copyright:		Shanghai Baosight Software Co., Ltd.
 *
 *	Description:	$(Desp) .
 *
 *	@author:		shijunpu
 *	@version		??????	shijunpu	Initial Version
 *  @version	9/17/2012  baoyuansong  ��������Ԫ�ش�С�ӿ� .
*************************************************************************/
#include "TaskGroup.h"
#include "Device.h"
#include "DataBlock.h"
#include "MainTask.h"
#include "driversdk/cvdrivercommon.h"
#include "gettext/libintl.h"
#include "processdb/DriverApi.h"
#include "TaskBatchUpdateData.h"
#include "math.h"
#include "OPAPI.h"
#include <unordered_map>
//#define _(STRING) gettext(STRING)
#define _(STRING) STRING
#ifdef WIN32
#ifndef NO_EXCEPTION_ATTACHER
#include "common/RegularDllExceptionAttacher.h"
#pragma comment(lib, "ExceptionReport.lib")
#pragma comment(lib, "dbghelp.lib")
// global member to capture exception so as to analyze 
static CRegularDllExceptionAttacher	g_RegDllExceptAttacher;
#endif//#ifndef NO_EXCEPTION_ATTACHER
#endif

#ifdef _WIN32
#define CVDRIVER_EXPORTS extern "C" __declspec(dllexport)
#else //_WIN32
#define CVDRIVER_EXPORTS extern "C"
#endif //_WIN32

extern CCVLog g_CVDrvierCommonLog;

extern std::map<long, string>  g_mapTraceTag;
extern bool g_bNeedTrace;
extern ACE_Recursive_Thread_Mutex g_mtxMapTraceTags;

CSimpleThreadQueue<ACE_Message_Block*> g_BatchDataQueue;
CSimpleThreadQueue<ACE_Message_Block*> g_BatchDataQueue2;
CSimpleThreadQueue<ACE_Message_Block*> g_devStatusQueue;

long Quality2RetValue(unsigned short uQuality)
{
	if(uQuality == WORD_QUALITY_GOOD)
		return DRV_SUCCESS;

	if(uQuality == WORD_QUALITY_BAD) // UNKWN error
		return DATA_STATUS_UNKNOWN_REASON;

	if(uQuality == WORD_QUALITY_UNCERTAIN)
		return DATA_STATUS_UNKNOWN_REASON;

	QUALITY_STATE *quality = (QUALITY_STATE *)&uQuality;
	if(quality->nQuality == QUALITY_BAD) // һһ��Ӧ�Ĺ�ϵ
		return quality->nSubStatus;

	return DATA_STATUS_UNKNOWN_REASON;
}

unsigned short RetValue2Quality(long lRet)
{
	if(lRet == DRV_SUCCESS)
		return WORD_QUALITY_GOOD;
	if(lRet == DATA_STATUS_UNKNOWN_REASON)
		return WORD_QUALITY_UNCERTAIN;

	QUALITY_STATE quality;
	quality.nQuality	= QUALITY_BAD;
	quality.nSubStatus = lRet;
	quality.nLimit		= LIMIT_NOT_LIMITED;
	quality.nLeftOver  = 0;
	return (*(unsigned short *)&quality);
}

unsigned short RetValue2QualityEx(unsigned short nQuality,unsigned short nSubStatus,unsigned short nLimit,unsigned short nleftOver)
{

	QUALITY_STATE quality;
	quality.nQuality	= nQuality;
	quality.nSubStatus = nSubStatus;
	quality.nLimit		= nLimit;
	quality.nLeftOver  = nleftOver;
	return (*(unsigned short *)&quality);
}

CVDRIVER_EXPORTS CVDATABLOCK		*Drv_GetDataBlockInfo(DRVHANDLE hDataBlock)
{
	if(hDataBlock == NULL)
		return NULL;

	CDataBlock *pDataBlock = (CDataBlock *)hDataBlock;

	pDataBlock->m_outDataBlockInfo.pszName = pDataBlock->m_strName.c_str();
	pDataBlock->m_outDataBlockInfo.pszDesc = pDataBlock->m_strDesc.c_str();
	pDataBlock->m_outDataBlockInfo.pszBlockType = pDataBlock->m_strBlockType.c_str();
	pDataBlock->m_outDataBlockInfo.nCycleRate = pDataBlock->m_nPollRate;
	pDataBlock->m_outDataBlockInfo.nElemNum = pDataBlock->m_nElemNum;
	pDataBlock->m_outDataBlockInfo.nElemBits = pDataBlock->m_nElemBits;
	pDataBlock->m_outDataBlockInfo.nBlockDataSize = (unsigned int)ceil(pDataBlock->m_nElemNum * pDataBlock->m_nElemBits / 8.0f);
	pDataBlock->m_outDataBlockInfo.pszAddress = pDataBlock->m_strAddress.c_str();
	pDataBlock->m_outDataBlockInfo.pszParam1 = pDataBlock->m_strParam1.c_str();
	pDataBlock->m_outDataBlockInfo.pszParam2 = pDataBlock->m_strParam2.c_str();
	pDataBlock->m_outDataBlockInfo.pszParam3 = pDataBlock->m_strParam3.c_str();
	return &pDataBlock->m_outDataBlockInfo;
}

CVDRIVER_EXPORTS CVDEVICE		*Drv_GetDeviceInfo(DRVHANDLE hDevice)
{
	if(hDevice == NULL)
		return NULL;

	CDevice *pDevice = (CDevice *)hDevice;
	pDevice->m_outDeviceInfo.pszName = pDevice->m_strName.c_str();
	pDevice->m_outDeviceInfo.pszDesc = pDevice->m_strDesc.c_str();
	pDevice->m_outDeviceInfo.nCycleRate = pDevice->m_nPollRate;
	pDevice->m_outDeviceInfo.nRecvTimeout = pDevice->m_nRecvTimeOut;
	pDevice->m_outDeviceInfo.pszConnType = pDevice->m_strConnType.c_str();
	pDevice->m_outDeviceInfo.pszConnParam = pDevice->m_strConnParam.c_str();
	
	pDevice->m_outDeviceInfo.pszParam1 = pDevice->m_strParam1.c_str();
	pDevice->m_outDeviceInfo.pszParam2 = pDevice->m_strParam2.c_str();
	pDevice->m_outDeviceInfo.pszParam3 = pDevice->m_strParam3.c_str();
	return &(pDevice->m_outDeviceInfo);
}

CVDRIVER_EXPORTS void Drv_SetUserData(DRVHANDLE h, int iIndex, int lUserData)
{
	if (NULL != h)
	{
		CDrvObjectBase *pDrvObj = (CDrvObjectBase *)h;
		pDrvObj->SetUserData(iIndex, lUserData);
	}
}

CVDRIVER_EXPORTS int Drv_GetUserData(DRVHANDLE h, int iIndex)
{
	if (NULL != h)
	{
		CDrvObjectBase *pDrvObj = (CDrvObjectBase *)h;
		return pDrvObj->GetUserData(iIndex);
	}

	return 0;
}

CVDRIVER_EXPORTS void*	Drv_GetUserDataPtr(DRVHANDLE hDrv, int iIndex)
{
	if (NULL != hDrv)
	{
		CDrvObjectBase *pDrvObj = (CDrvObjectBase *)hDrv;
		return pDrvObj->GetUserPtr(iIndex);
	}

	return NULL;
}

CVDRIVER_EXPORTS void	Drv_SetUserDataPtr(DRVHANDLE hDrv, int iIndex, void *pUserData)
{
	if (NULL != hDrv)
	{
		CDrvObjectBase *pDrvObj = (CDrvObjectBase *)hDrv;
		pDrvObj->SetUserPtr(iIndex, pUserData);
	}
}

/**
 *  $(Desp) .
 *  $(Detail) .
 *
 *  @param		-[in]  DRVHANDLE hDataBlock : ���ݿ���
 *  @param		-[in]  int nElemBits : Ԫ�صĴ�С����λ��λ
 *  @return		CVDRIVER_EXPORTS void.
 *
 *  @version	9/17/2012  baoyuansong  Initial Version.
 */
CVDRIVER_EXPORTS void Drv_SetElemBits(DRVHANDLE hDataBlock, int nElemBits)
{
	if (NULL != hDataBlock)
	{
		CDataBlock *pDataBlock = (CDataBlock *)hDataBlock;
		pDataBlock->m_nElemBits = nElemBits;
	}
}

CVDRIVER_EXPORTS void Drv_SetElemNum(DRVHANDLE hDataBlock, int nNum)
{
	if (NULL != hDataBlock)
	{
		CDataBlock *pDataBlock = (CDataBlock *)hDataBlock;
		pDataBlock->m_nElemNum = nNum;
	}
}

CVDRIVER_EXPORTS void Drv_SetStartAddr(DRVHANDLE hDataBlock, const char* szStartAddr)
{
	if (NULL != hDataBlock)
	{
		CDataBlock *pDataBlock = (CDataBlock *)hDataBlock;
		pDataBlock->m_strAddress = szStartAddr;
	}
}

// ���ط��͵��ֽڸ���
CVDRIVER_EXPORTS int Drv_SendToDevice(DRVHANDLE hDevice, char *szBuffer, long lBufLen, long lTimeOutMS )
{
	if(hDevice == NULL)
	{
		CV_ERROR(g_CVDrvierCommonLog, -2, _("Failed to send the request to the device(%d bytes, %d timeout), because device handle is empty!"),//���豸��������ʧ��(���� %d ���ֽڣ���ʱ %d ), �����豸����?��
			lBufLen, lTimeOutMS);
		return -2;
	}

	CDevice *pDevice = (CDevice *)hDevice;
	return pDevice->SendToDevice(szBuffer, lBufLen, lTimeOutMS);
}

// �����յ����ֽڸ���
CVDRIVER_EXPORTS int Drv_RecvFromDevice(DRVHANDLE hDevice, char *szBuff, long lBufLen, long lTimeOutMS)
{
	if(hDevice == NULL)
		return -2;

	CDevice *pDevice = (CDevice *)hDevice;
	return pDevice->RecvFromDevice(szBuff, lBufLen, lTimeOutMS);
}

// ����N�ֽڣ������յ����ֽڸ���
CVDRIVER_EXPORTS int Drv_Recv_NFromDevice( DRVHANDLE hDevice, char *szBuffer, long lBufLen, long lTimeOutMS )
{
	if(hDevice == NULL)
		return -2;

	CDevice *pDevice = (CDevice *)hDevice;
	return pDevice->RecvNBytesFromDevice(szBuffer, lBufLen, lTimeOutMS);
}

CVDRIVER_EXPORTS int Drv_DisconnectDevice(DRVHANDLE hDevice)
{
	if(hDevice == NULL)
		return -2;

	CDevice *pDevice = (CDevice *)hDevice;
	return pDevice->DisonnectDevice();
}

CVDRIVER_EXPORTS int Drv_ReConnectDevice(DRVHANDLE hDevice, long lTimeOutMS)
{
	if(hDevice == NULL)
		return -2;

	CDevice *pDevice = (CDevice *)hDevice;
	return pDevice->ReConnectDevice(lTimeOutMS);
}

CVDRIVER_EXPORTS void Drv_ClearRecvBuffer(DRVHANDLE hDevice)
{
	if(hDevice == NULL)
		return;

	CDevice *pDevice = (CDevice *)hDevice;
	pDevice->ClearRecvBuffer();
}

CVDRIVER_EXPORTS void* Drv_GetTaskGroupReactor(DRVHANDLE hDevice)
{
    if(hDevice == NULL)
        return NULL;

    CDevice *pDevice = (CDevice *)hDevice;

    return (void*)(pDevice->m_pTaskGroup)->m_pReactor;
}

DRVHANDLE Drv_GetDataBlockByName(DRVHANDLE hDevice, const char *szBlockName)
{
	if (NULL == hDevice || !szBlockName)
		return NULL;

	CDevice *pDevice = (CDevice *)hDevice;
	// ���ڵ�ǰ�豸����
	std::map<std::string, CDataBlock*>::iterator itDb = pDevice->m_mapDataBlocks.find(string(szBlockName));
	if (itDb != pDevice->m_mapDataBlocks.end())
		return (CDataBlock*)itDb->second;

	CDataBlock *pDataBlock = NULL;
	// �Ҳ���ʱ������������������豸����?
	CDriver &driver = pDevice->m_pTaskGroup->m_pMainTask->m_driverInfo;
	std::map<string, CTaskGroup*>::iterator itTaskGrp = driver.m_mapTaskGroups.begin();	
	// �����豸�飬�����豸
	for ( ; itTaskGrp != driver.m_mapTaskGroups.end(); itTaskGrp++)
	{
		CTaskGroup *pTmpTaskGroup = itTaskGrp->second;
		std::map<string, CDevice*>::iterator iter_device = pTmpTaskGroup->m_mapDevices.find(pDevice->m_strName);
		if(iter_device !=  pTmpTaskGroup->m_mapDevices.end())
		{
			CDevice *pTmpDevice = iter_device->second;
			std::map<string, CDataBlock*>::iterator itDataBlock = pTmpDevice->m_mapDataBlocks.find(szBlockName);
			if(itDataBlock != pTmpDevice->m_mapDataBlocks.end())
			{
				pDataBlock = itDataBlock->second;
				return pDataBlock;
			}
		}
	}

	return NULL;
}

CVDRIVER_EXPORTS void Drv_SetConfigChanged(DRVHANDLE hDevice, int nIsChanged)
{
	if (NULL != hDevice)
	{
		CDrvObjectBase *pDeviceObj = (CDrvObjectBase *)hDevice;
		pDeviceObj->m_bConfigChanged = (nIsChanged != 0);
	}
}

CVDRIVER_EXPORTS long Drv_IsActiveHost()
{
	// 1: active 因为已经去除了冗余机制，所以这里直接返�?1
	// return 1;
	long lStatus = 0;
	GetRMStatus(&lStatus);
	return lStatus;
}

// �������ݿ�����ݡ�״�?��ʱ���?
CVDRIVER_EXPORTS long Drv_UpdateBlockData(DRVHANDLE hDevice, DRVHANDLE hDataBlock, 
	 const char *szBuffer, long lOffset, long lBufLen, long lStatus, DRVTIME* ptvTimeStamp)
{
	int32 nRet = ICV_SUCCESS;
	if (NULL == hDevice || NULL == hDataBlock || NULL == szBuffer)
		return -1;

	CDevice *pDevice = (CDevice *)hDevice;
	CDataBlock *pDataBlock = (CDataBlock *)hDataBlock;
	//���±���block buffer
	pDataBlock->UpdateBlockBuffer(szBuffer, lOffset, lBufLen);

	unsigned short uQuality = RetValue2Quality(lStatus);

	// ���ö�������ʱ���?
	TCV_TimeStamp cvCurrentTime = (timeval)ACE_OS::gettimeofday();
	if(ptvTimeStamp != NULL)
	{
		 cvCurrentTime.tv_sec = ptvTimeStamp->tv_sec;
		 cvCurrentTime.tv_usec = ptvTimeStamp->tv_usec;
	}

	//������Ҫ���µ�tag��
	std::vector<TProtoIDVTQ> vecData;
	int32 nVecSize = pDataBlock->m_vecTagAddrID.size();
	int32 i;
	int32 nTagAllLen;
	for(i = 0; i < nVecSize; i++)
	{
		TagAddrID* pTagAddrID = pDataBlock->m_vecTagAddrID[i];
		if(pTagAddrID->nBitOffset >= lOffset*BITS_PER_BYTE) 
		{
			if(((pTagAddrID->nBitOffset + pTagAddrID->nBitLen)%BITS_PER_BYTE) == 0)
			{
				nTagAllLen = (pTagAddrID->nBitOffset + pTagAddrID->nBitLen)/BITS_PER_BYTE;
			}
			else
			{
				nTagAllLen = ((pTagAddrID->nBitOffset + pTagAddrID->nBitLen)/BITS_PER_BYTE) + 1;
			}
			if(nTagAllLen <= (lOffset + lBufLen))
			{
				TProtoIDVTQ vtqData;
				int32 nBufOffset = pTagAddrID->nBitOffset/BITS_PER_BYTE - lOffset;
				vtqData.m_nDataType = pTagAddrID->nDataType;
				vtqData.m_nTagID = pTagAddrID->nTagID;
				vtqData.m_nSec = cvCurrentTime.tv_sec;
				vtqData.m_nMs = cvCurrentTime.tv_usec / 1000;

				if(pTagAddrID->nMDIAddrNo == 0)
				{
					if(vtqData.m_nDataType != DT_BIT)
					{
						vtqData.m_nLenBuf = pTagAddrID->nBitLen/BITS_PER_BYTE;
						vtqData.m_pBuf = new char[vtqData.m_nLenBuf];

						memcpy(vtqData.m_pBuf, szBuffer + nBufOffset, vtqData.m_nLenBuf);					
					}
					else
					{
						char* pBitData = new char[1];
						char Bittemp = *((char*)szBuffer + nBufOffset);
						int32 nTagBitOffset = pTagAddrID->nBitOffset%BITS_PER_BYTE;
						Bittemp = (Bittemp>>nTagBitOffset) & ((char)1);
						Bittemp = Bittemp<<2;
						Bittemp = Bittemp>>2; //����λ����
						*pBitData = Bittemp;
						vtqData.m_pBuf = pBitData;
						vtqData.m_nLenBuf = 1;
					}
				}
				else
				{
					if(vtqData.m_nDataType != DT_BIT)
					{
						char nData;
						uint8 nAddrNoTemp = 0;
						char* pBitData = new char[1];
						nData = *(char*)(szBuffer + nBufOffset);
						nAddrNoTemp = pTagAddrID->nMDIAddrNo << 6;
						nData = nData << pTagAddrID->nMDIAddrNo;
						nData = nData<<2;
						nData = nData>>2; //����λ����
						nData = nData | nAddrNoTemp;
						*pBitData = nData;
						vtqData.m_pBuf = (char*)pBitData;
						vtqData.m_nLenBuf = 1;
					}
					else
					{
						char* pBitData = new char[1];
						uint8 nAddrNoTemp = 0;
						char Bittemp = *((char*)szBuffer + nBufOffset);
						int32 nTagBitOffset = pTagAddrID->nBitOffset%BITS_PER_BYTE;

						Bittemp = (Bittemp>>nTagBitOffset) & ((char)1);
						Bittemp = Bittemp << pTagAddrID->nMDIAddrNo;

						Bittemp = Bittemp<<2;
						Bittemp = Bittemp>>2; //����λ����

						nAddrNoTemp = pTagAddrID->nMDIAddrNo << 6;
						Bittemp = Bittemp | nAddrNoTemp;

						*pBitData = Bittemp;
						vtqData.m_pBuf = pBitData;
						vtqData.m_nLenBuf = 1;
					}
				}
				vtqData.m_nQuality = uQuality;

				if(g_pfnOnBatchUpdateData)
				{
					char *pBuf = NULL;
					int32 nLenBuf = 0;
					const int nMAXQUEUESIZE = 500000;

					if(g_BatchDataQueue.size() < nMAXQUEUESIZE)
					{
						proto_cvdrvcomm_batch_updatedata_pack(&vtqData,&pBuf,&nLenBuf);
						ACE_Message_Block *pMb = new ACE_Message_Block(nLenBuf);
						pMb->copy(pBuf, nLenBuf);
						proto_cvdrvcomm_batch_updatedata_pack_free(pBuf);
						SAFE_DELETE_ARRAY(vtqData.m_pBuf);
						g_BatchDataQueue.enqueue(pMb);
					}
					else
					{
						CV_ERROR(g_CVDrvierCommonLog,-1,"g_BatchDataQueue.size out of range %d !",g_BatchDataQueue.size());
					}
				}
				else
					vecData.push_back(vtqData);
			}
		}	
	}

	if(g_pfnOnBatchUpdateData)
		return (long)nRet;

	if(vecData.size() > 0)
	{
		if (g_bNeedTrace)
		{
			for (vector<TProtoIDVTQ>::const_iterator iter = vecData.begin(); iter != vecData.end(); iter++)
			{
				ACE_Guard<ACE_Recursive_Thread_Mutex> lockguard(g_mtxMapTraceTags);
				{
					std::map<long, string>::iterator itMap = g_mapTraceTag.find(iter->m_nTagID);
					if (itMap != g_mapTraceTag.end())
					{
						TRACE_TAG_MSG tracetagmsg;
						char szVTQ[PDB_MAX_TEXT_VALUE_LEN];
						strcpy(tracetagmsg.szTagName, itMap->second.c_str());
						strcpy(tracetagmsg.szModName, "driver");
						QUALITY_STATE quality = (*(QUALITY_STATE *)&iter->m_nQuality);
						if (quality.nQuality == QUALITY_GOOD)
						{
							tracetagmsg.bStatus = TRATAG_STATUS_GOOD;
							CastTypeToASCII(iter->m_pBuf, iter->m_nLenBuf, iter->m_nDataType, szVTQ, sizeof(szVTQ));
							sprintf(tracetagmsg.strMsg, "good quality. value:%s type:%d", szVTQ, iter->m_nDataType);
						}
						else
						{
							tracetagmsg.bStatus = TRATAG_STATUS_BAD;
							sprintf(szVTQ, "tag bad quality!  quality %d", iter->m_nQuality);
						}
						tracetagmsg.time = ACE_OS::gettimeofday();
						// nRet = OP_API_Write_Trace_Tags_Msg(tracetagmsg);
						// CV_CHECK_FAIL_LOG(g_CVDrvierCommonLog, nRet, nRet, "OP_API_Write_Trace_Tags_Msg");
					}
				}	
			}	
		}
		nRet = CVDrv_SaveData(&vecData);	
	}
	for(i = 0; i < vecData.size(); i++)
	{
		if(vecData[i].m_pBuf != NULL)
		{
			delete [] vecData[i].m_pBuf;
		}
	}
	CV_DEBUG(g_CVDrvierCommonLog, " device (%s), the datablock (%s): %d bytes, nRet : %d, vecTag size : %d!",pDevice->m_strName.c_str(), pDataBlock->m_strName.c_str(), lBufLen, nRet, vecData.size());
	return (long)nRet;
}


// �������ݿ��״�?���������ϣ���hDrvDataBlock��ʾ�����豸�µ����п�
long UpdateBlockStatus(DRVHANDLE hDevice, DRVHANDLE hDataBlock, unsigned short uQuality)
{
	CDevice *pDevice = (CDevice *)hDevice;
	CDataBlock *pDataBlock = (CDataBlock *)hDataBlock;
	int32 nRet = ICV_SUCCESS;

	if (NULL == hDevice || NULL == hDataBlock)
		return -1;

	//������Ҫ���µ�tag��
	std::vector<int32> vecTagID;
	int32 nVecSize = pDataBlock->m_vecTagAddrID.size();
	int32 i;
	for(i = 0; i < nVecSize; i++)
	{
		TagAddrID* pTagAddrID = pDataBlock->m_vecTagAddrID[i];

		TCV_TimeStamp cvTimeStamp = (timeval)ACE_OS::gettimeofday();
		
		if(pTagAddrID->nTagID > 0)
		{
			if(g_pfnOnBatchUpdateData)
			{
				char *pBuf = NULL;
				int32 nLenBuf = 0;
				const int nMAXQUEUESIZE = 500000;

				TProtoIDVTQ2 vtqData;
				vtqData.m_nTagID = pTagAddrID->nTagID;
				vtqData.m_cvTimeStamp = cvTimeStamp;
				vtqData.m_nQuality = uQuality;

				if(g_BatchDataQueue2.size() < nMAXQUEUESIZE)
				{
					proto_cvdrvcomm_batch_updatedata_pack2(&vtqData,&pBuf,&nLenBuf);
					ACE_Message_Block *pMb = new ACE_Message_Block(nLenBuf);
					pMb->copy(pBuf, nLenBuf);
					proto_cvdrvcomm_batch_updatedata_pack_free(pBuf);
					g_BatchDataQueue2.enqueue(pMb);
				}
				else
				{
					CV_ERROR(g_CVDrvierCommonLog,-1,"g_BatchDataQueue2.size out of range %d !",g_BatchDataQueue2.size());
				}
			}
			else
				vecTagID.push_back(pTagAddrID->nTagID);
		}
	}

	if(g_pfnOnBatchUpdateData)
		return (long)nRet;

	TCV_TimeStamp cvTimeStamp = (timeval)ACE_OS::gettimeofday();

	if(vecTagID.size() > 0)
	{
		nRet = CVDrv_SetBlockQuality(vecTagID, &cvTimeStamp, uQuality);
	}

	CV_DEBUG(g_CVDrvierCommonLog, "CVDrv_SetBlockQuality (%s), the datablock (%s), uQuality : %d !",pDevice->m_strName.c_str(), pDataBlock->m_strName.c_str(), uQuality);//���豸(%s)�����ݿ�(%s)д�� %d ���ֽ�!
	return (long)nRet;
}

CVDRIVER_EXPORTS long Drv_UpdateBlockStatus(DRVHANDLE hDevice, DRVHANDLE hDataBlock, long lStatus)
{
	unsigned short uQuality = RetValue2Quality(lStatus);
	return UpdateBlockStatus(hDevice, hDataBlock, uQuality);
}

//
CVDRIVER_EXPORTS long	Drv_UpdateBlockStatusEx(DRVHANDLE hDevice, DRVHANDLE hDataBlock, int nQuality, int nSubQuality, int nLimit, int nLeftOver)
{
	unsigned short uQuality = RetValue2QualityEx(nQuality,nSubQuality,nLimit,nLeftOver);
	return UpdateBlockStatus(hDevice, hDataBlock, uQuality);
}

// ��ȡ���ݿ���Ϣ�����ڿ��豸���а�λ����ʱ���Ȼ�ȡtag���Ӧ�Ŀ��ֵ��Ȼ���tag������λ����
CVDRIVER_EXPORTS long Drv_GetDataBlockData(DRVHANDLE hDevice, DRVHANDLE hDataBlock, char *szBuffer, long nOffset, long nBytesCount, long *plStatus, DRVTIME* ptvTimeStamp)
{
	if (NULL == hDevice || NULL == hDataBlock || NULL == szBuffer)
		return -1;

	CDevice *pDevice = (CDevice *)hDevice;
	CDataBlock *pDataBlock = (CDataBlock *)hDataBlock;

	if(pDataBlock->m_szBlockBuffer != NULL)
		memcpy(szBuffer, pDataBlock->m_szBlockBuffer + nOffset, nBytesCount);
	return DRV_SUCCESS;
}



CVDRIVER_EXPORTS long Drv_IsDeviceConfigChanged(DRVHANDLE hDevice)
{
	if (NULL == hDevice)
		return 1;

	CDrvObjectBase *pDrvObj = (CDrvObjectBase *)hDevice;
	return pDrvObj->m_bConfigChanged;
}

CVDRIVER_EXPORTS long Drv_IsDataBlockConfigChanged(DRVHANDLE hDataBlock)
{
	if (NULL == hDataBlock)
		return 1;

	CDrvObjectBase *pDrvObj = (CDrvObjectBase *)hDataBlock;
	return pDrvObj->m_bConfigChanged;
}

#define MAX_LOGTEMP_SIZE 4096
void Drv_LogMessage(int nLogLevel, const char *fmt,... )
{
	char szLogString[MAX_LOGTEMP_SIZE];
	va_list ap;
	va_start(ap,fmt);
	vsnprintf(szLogString,MAX_LOGTEMP_SIZE, fmt,ap);
	va_end(ap);
	g_CVDrvierCommonLog.LogMessage(nLogLevel, szLogString);
}

CVDRIVER_EXPORTS void Drv_GetCurrentTime(DRVTIME *pDrvTime)	// ��ȡ��ǰʱ��
{
	if(pDrvTime)
	{
		ACE_Time_Value tvNow = ACE_OS::gettimeofday();
		pDrvTime->tv_sec = (unsigned long)tvNow.sec();
		pDrvTime->tv_usec = (unsigned long)tvNow.usec();
	}
}

/**
 *  $(Desp) ����tcp����ʱ����ʹ�õĶ˿�.
 *  $(Detail) .
 *
 *  @param		-[in,]  DRVHANDLE hDevice : �豸���?
 *  @param		-[in]  unsigned short usPort : ָ���������ӷ�����ʹ�õĶ˿�
 *  @return		CVDRIVER_EXPORTS long.
 *
 *  @version	8/13/2013  baoyuansong  Initial Version.
 */
CVDRIVER_EXPORTS int Drv_SetTcpClientLocalPort( DRVHANDLE hDevice, unsigned short usPort )
{
	if (NULL == hDevice)
		return 1;

	CDevice *pDevice = (CDevice *)hDevice;
	if(ACE_OS::strcasecmp(pDevice->m_strConnType.c_str(), "tcpclient") == 0)
	{
		char szBuf[32] = {0};
		sprintf(szBuf, ";localport=%d", (int)usPort);
		pDevice->m_strConnParam.append(szBuf);
	}
	
	return 0;
}

CVDRIVER_EXPORTS int Drv_SetDeviceMultiLinkFlag(DRVHANDLE hDevice, bool bMultiLinkFlag)
{
	if (NULL == hDevice)
		return 1;

	CDevice *pDevice = (CDevice *)hDevice;
	pDevice->m_bMultiLink = bMultiLinkFlag;

	return DRV_SUCCESS;
}

/**
 *  $(Desp) ��ȡ�豸�Ƿ����ӣ����з���true����Ϊ�����ӣ�����false��Ϊ������.
 *  $(Detail) .
 *
 *  @param		-[in,]  DRVHANDLE hDevice : �豸���?
 *  @return		CVDRIVER_EXPORTS bool.
 *
 *  @version	4/14/2016  zhangqiang  Initial Version.
 */
CVDRIVER_EXPORTS bool Drv_IsSingleLink(DRVHANDLE hDevice)
{
	if (NULL == hDevice)
		return false;

	CDevice *pDevice = (CDevice *)hDevice;
	return pDevice->m_bMultiLink == false;
}
/**
 *  $(Desp) ��ȡ�豸����״̬�����з���true����Ϊ����״̬������false��Ϊδ����״̬.
 *  $(Detail) .
 *
 *  @param		-[in,]  DRVHANDLE hDevice : �豸���?
 *  @return		CVDRIVER_EXPORTS bool.
 *
 *  @version	4/14/2016  zhangqiang  Initial Version.
 */
CVDRIVER_EXPORTS bool Drv_IsConnected(DRVHANDLE hDevice)
{
	if (NULL == hDevice)
		return false;

	CDevice *pDevice = (CDevice *)hDevice;
	if(NULL == pDevice->m_pDeviceConnection)
		return false;
	return pDevice->m_pDeviceConnection->IsConnected();
}

/**
 *  $(Desp) ��ȡ��ǰ���ӵ��豸�������Ǳ������з���0δ֪������1�����豸������2�Ǳ��豸
 *  $(Detail) .
 *
 *  @param		-[in,]  DRVHANDLE hDevice : �豸���?
 *  @return		CVDRIVER_EXPORTS bool.
 *
 *  @version	8/8/2016  zhangqiang  Initial Version.
 */
CVDRIVER_EXPORTS long Drv_GetConnectedState(DRVHANDLE hDevice)
{
	if (NULL == hDevice)
		return 0;

	CDevice *pDevice = (CDevice *)hDevice;
	if(ACE_OS::strcasecmp(pDevice->m_strConnType.c_str(), "tcpclient") == 0)
	{
		CTcpClientHandler* pDeviceConnection = (CTcpClientHandler*)pDevice->m_pDeviceConnection;
		if(pDeviceConnection)
			return pDeviceConnection->GetConnectedState();
	}
	return 0;
}

CVDRIVER_EXPORTS void Drv_SetRecvCallBackFunc(DRVHANDLE hDevice, PFN_CVCommRecvCallBack pfnRecvFunc, void* pCallbackParam, long lCallbackParam, char *pszCallbackParam)
{
	if (NULL == hDevice)
		return;

	CDevice *pDevice = (CDevice *)hDevice;
	pDevice->m_pfnRecvCallback = pfnRecvFunc;
	pDevice->m_pCallbackParam = pCallbackParam;
	pDevice->m_lCallbackParam = lCallbackParam;
	if(pszCallbackParam != NULL)
		memcpy(pDevice->m_szCallbackParam, pszCallbackParam, ICV_DEVICENAME_MAXLEN);
	return;
}

/**
 *  $(Desp) ��ȡ���豸�����ݿ�ĸ���?
 *  $(Detail) .
 *
 *  @param		-[in]  DRVHANDLE hDevice : �豸���?
 *  @return		CVDRIVER_EXPORTS long ���ݿ����?.
 *
 *  @version	9/22/2016  zhangqiang  Initial Version.
 */
long Drv_GetDataBlockCount(DRVHANDLE hDevice)
{
	if(hDevice == NULL)
		return 0;

	CDevice *pDevice = (CDevice *)hDevice;
	return pDevice->m_mapDataBlocks.size();
}

/**
 *  $(Desp) ��ȡ���豸���������ݿ�
 *  $(Detail) .
 *
 *  @param		-[in]  DRVHANDLE hDevice : �豸���?
 *  @param		-[in,out]  CVDATABLOCK* pDataBlock : ���ݿ�����
 *  @param		-[in,out]  long lCount : ���ݿ����?
 *  @return		CVDRIVER_EXPORTS long.
 *
 *  @version	9/22/2016  zhangqiang  Initial Version.
 */
long Drv_GetDatablocks(DRVHANDLE hDevice, CVDATABLOCK* pDataBlock, long lCount)
{
	if(hDevice == NULL || pDataBlock == NULL)
		return -1;

	CDevice *pDevice = (CDevice *)hDevice;
	std::map<std::string, CDataBlock*>::iterator iter = pDevice->m_mapDataBlocks.begin();
	for (int i = 0; i < lCount && iter != pDevice->m_mapDataBlocks.end(); ++ i, ++ iter)
	{
		(pDataBlock + i)->pszName = iter->second->m_strName.c_str();
		(pDataBlock + i)->pszDesc = iter->second->m_strDesc.c_str();
		(pDataBlock + i)->pszBlockType = iter->second->m_strBlockType.c_str();
		(pDataBlock + i)->nCycleRate = iter->second->m_nPollRate;
		(pDataBlock + i)->nElemNum = iter->second->m_nElemNum;
		(pDataBlock + i)->nElemBits = iter->second->m_nElemBits;
		(pDataBlock + i)->pszAddress = iter->second->m_strAddress.c_str();
		(pDataBlock + i)->pszParam1 = iter->second->m_strParam1.c_str();
		(pDataBlock + i)->pszParam2 = iter->second->m_strParam2.c_str();
		(pDataBlock + i)->pszParam3 = iter->second->m_strParam3.c_str();
	}
	return ICV_SUCCESS;
}

/**
 *  $(Desp) дֵʱ���øýӿڻ�ȡ�������?
 *  $(Detail) .
 *
 *  @param		-[in]  DRVHANDLE hDataBlock : ���ݿ���
 *  @param		-[in]  int nTagByteOffset : �ֽ�ƫ��
 *  @param		-[in]  int nTagBitOffset : λƫ��
 *  @param		-[in,out]  int nType : �������?
 *  @return		CVDRIVER_EXPORTS int �Ƿ��ҵ�.
 *
 *  @version	9/22/2016  zhangqiang  Initial Version.
 */
CVDRIVER_EXPORTS long Drv_GetTagType(DRVHANDLE hDataBlock, int nTagByteOffset, int nTagBitOffset, int *nType)
{
	if(hDataBlock == NULL)
		return -1;

	CDataBlock *pDataBlock = (CDataBlock *)hDataBlock;

	std::vector<TagAddrID*>::iterator iter;
	for (iter = pDataBlock->m_vecTagAddrID.begin(); iter != pDataBlock->m_vecTagAddrID.end(); iter++)
	{
		if ((*iter)->nBitOffset == nTagByteOffset*BITS_PER_BYTE + nTagBitOffset)
		{
			*nType = (int)(*iter)->nDataType;
			return DRV_SUCCESS;
		}	
	}
	
	return -1;
}


/**
 *  $(Desp) ��ȡ���ݿ�������ָ�����͵ĵ���?�ƺͳ���
 *  $(Detail) .
 *
 *  @param		-[in]  DRVHANDLE hDataBlock : ���ݿ���
 *  @param		-[in]  int nType : ��ҪѰ�ҵ�����
 *  @param		-[in,out]  map byteOffSets : keyΪ��Ӧ���͵ĵ������ݿ��е�ƫ��,valueΪ��Ӧ�ֽڳ���
 *  @return		CVDRIVER_EXPORTS int �Ƿ��ҵ�.
 *
 *  @version	9/22/2016  zhangqiang  Initial Version.
 */
CVDRIVER_EXPORTS bool Drv_BlockContainType(int nType, int*& nbyteOffset, int*& nTagLength, int *nArrayLength, DRVHANDLE hDataBlock)
{
	if(hDataBlock == NULL)
		return false;

	CDataBlock *pDataBlock = (CDataBlock *)hDataBlock;
	int nLenBytes = 0;
	int i = 0;
	vector<int> vecOffset;
	vector<int> vecTagLen;
	std::vector<TagAddrID*>::iterator iter;
	for (iter = pDataBlock->m_vecTagAddrID.begin(); iter != pDataBlock->m_vecTagAddrID.end(); iter++)
	{
		if ((*iter)->nDataType == nType)
		{
			nLenBytes = (*iter)->nBitLen / BITS_PER_BYTE;
			if ((*iter)->nBitLen % BITS_PER_BYTE)
				nLenBytes++;

			vecOffset.push_back((*iter)->nBitOffset);
			vecTagLen.push_back(nLenBytes);
			i++;
		}	
	}
	
	*nArrayLength = i;
	int* intOffset = new int[i];
	int* intTagLen = new int[i];
	for (int j = 0; j < i; j++)
	{
		intOffset[j] = vecOffset[j];
		intTagLen[j] = vecTagLen[j];
	}
	nbyteOffset = intOffset;
	nTagLength = intTagLen;
	if (i > 0)
		return true;
	else
		return false;
	
}


void proto_devstatus_pack(const char* szDevName, const TCV_TimeStamp* pCVTime, long nDrvStatus, char** ppBuf, int32* pnLen)
{
	*pnLen = sizeof(long) + strlen(szDevName)+1+sizeof(TCV_TimeStamp)+sizeof(long);
	*ppBuf = new char[*pnLen];
	char* pBufPt = *ppBuf;
	
	long lDrvNameLen = strlen(szDevName)+1;
	memcpy(pBufPt, &lDrvNameLen, sizeof(long));
	pBufPt += sizeof(long);

	memcpy(pBufPt, szDevName, strlen(szDevName)+1);
	pBufPt += strlen(szDevName)+1;

	memcpy(pBufPt, pCVTime, sizeof(TCV_TimeStamp));
	pBufPt += sizeof(pCVTime);

	memcpy(pBufPt, &nDrvStatus, sizeof(long));
}

//FIXME: ɾ����ͷ�ļ�����ĺ�?��
#define DRV_DEV_SATATUS_QUEUE_MAX 100

CVDRIVER_EXPORTS long Drv_UpdateDevStatus(DRVHANDLE hDevice, long nDeviceStatus)
{
	if (NULL == hDevice)
		return 1;

	 if (g_devStatusQueue.size()< DRV_DEV_SATATUS_QUEUE_MAX)
	{
		CDevice *pDevice = (CDevice *)hDevice;
		TCV_TimeStamp cvTimeStamp = (timeval)ACE_OS::gettimeofday();
		char* pBuf = NULL;
		int32 nLenBuf = 0;
		proto_devstatus_pack(pDevice->m_strName.c_str(), &cvTimeStamp, nDeviceStatus, &pBuf, &nLenBuf);
		ACE_Message_Block*  pMsg = new ACE_Message_Block(nLenBuf);
		pMsg->copy(pBuf, nLenBuf);
		SAFE_DELETE_ARRAY(pBuf);
		g_devStatusQueue.enqueue(pMsg);
	}

	return ICV_SUCCESS;
}

/**
 *  $(Desp) ��ȡ���ݿ���Tag����Ϣ
 *  $(Detail) .
 *
 *  @param		-[in]  DRVHANDLE hDataBlock : ���ݿ���
 *  @param		-[in,out]  std::vector<TagInfoInBlock* > vecTagInfo : ����Ϣ
 *  @return		CVDRIVER_EXPORTS int �Ƿ��ҵ�.
 *
 *  @version	10/12/2021  zhangliuqing  Initial Version.
 */
CVDRIVER_EXPORTS long Drv_GetTagInfoInBlock(DRVHANDLE hDataBlock, std::vector<TagInfoInBlock>& vecTagInfo)
{
	if(hDataBlock == NULL)
		return -1;

	CDataBlock *pDataBlock = (CDataBlock *)hDataBlock;
	for(int i = 0; i < pDataBlock->m_vecTagAddrID.size(); ++i)
	{
		TagInfoInBlock taginfo;
		taginfo.nBitLen = pDataBlock->m_vecTagAddrID[i]->nBitLen;
		taginfo.nBitOffset = pDataBlock->m_vecTagAddrID[i]->nBitOffset;
		taginfo.nTagID = pDataBlock->m_vecTagAddrID[i]->nTagID;
		taginfo.nDataType =  pDataBlock->m_vecTagAddrID[i]->nDataType;
		taginfo.nMDIAddrNo =  pDataBlock->m_vecTagAddrID[i]->nMDIAddrNo;
		taginfo.nOverBlock =  pDataBlock->m_vecTagAddrID[i]->nOverBlock;
		vecTagInfo.push_back(taginfo);
	}

	return ICV_SUCCESS;
}


static int getDataSize(int dataType)
{
	int nElemNum = 0;

	switch (dataType)
	{
	case TAG_DATATYPE_LREAL:
	case TAG_DATATYPE_LINT:
	case TAG_DATATYPE_ULINT:
	case TAG_DATATYPE_LWORD:
		nElemNum = 8;
		break;

	case TAG_DATATYPE_INT:
	case TAG_DATATYPE_UINT:
	case TAG_DATATYPE_WORD:
		nElemNum = 2;
		break;

	case TAG_DATATYPE_REAL:
	case TAG_DATATYPE_UDINT:
	case TAG_DATATYPE_DINT:
	case TAG_DATATYPE_DWORD:
		nElemNum = 4;
		break;

	case TAG_DATATYPE_BOOL:
	case TAG_DATATYPE_SINT:
	case TAG_DATATYPE_USINT:
	case TAG_DATATYPE_BYTE:
	case TAG_DATATYPE_CHAR:
		nElemNum = 1;
		break;
	case TAG_DATATYPE_STRING:
		// �ݶ�Ϊ256
		nElemNum = 256;
		break;
	default:
		break;

		return nElemNum;
	}
}

/**
 *  ������ĵ��鰴ɨ�����ڷ���
 *  [in] const TagInfoVector &tagVec: �������ļ���
 *  [out] IntvTagGroups &tagGroup: ����ɨ�����ڷ���ĵ���ļ���; IntvTagGroups.first��ʾɨ������; IntvTagGroups.second��ʾ��ɨ�����ڷ���ĵ�, ���ڲ���nMaxSizePerGroup, ͬһ��ɨ�����ڿ��ܻ��ж������
 *  [in] const int nMinIntv: ��С���ڼ��, ��λms, Ĭ��Ϊ1000, ������ò�Ϊ0������ֵʱ, tag��ɨ�����ڻ�����ȡ������ֵ��������; �������Ϊ0, ��ÿ����ͬ��ɨ�����ڻ��½�һ��key
 *  [in] const int nMaxSizePerGroup: ɨ�����ڶ�Ӧ���������ֽ���, �����������������Ե��ζ����ֽ���������ʱ��������, Ĭ��ֵ0��ʾ������
 *
 *  return����ֵ: 0��ʾ����ɹ�
 */
CVDRIVER_EXPORTS long Drv_TagsToGroupsByScanIntv(const TagInfoVector &tagVec, IntvDeviceMap &tagGroup, const int nMinIntv, const int nMaxSizePerGroup)
{
	for (const TagInfo &tag : tagVec)
	{
		std::string devName;
		std::string tagAddress = std::string(tag.szAddress);

		std::size_t pos = tagAddress.find(':');
		if (pos == std::string::npos)
			continue;
		else
			devName = tagAddress.substr(0, pos);

		auto devIt = tagGroup.find(devName);
		if(devIt == tagGroup.end())
		{
			IntvTagGroups intvGroups;
			devIt = tagGroup.insert({devName, intvGroups}).first;
		}

		int nScanIntv = tag.nPollRate > 0 ? tag.nPollRate : 500;
		if (nMinIntv > 0)
			int nScanIntv = std::ceil(nScanIntv / nMinIntv) * nMinIntv;

		auto range = devIt->second.equal_range(nScanIntv);
		if (range.first == range.second)
		{
			TagInfoGroup grp{};
			grp.tagVec.emplace_back(tag);
			grp.groupSize += getDataSize(tag.nDataType);

			devIt->second.insert({nScanIntv, grp});
		}
		else
		{
			auto it = range.first;
			for (auto iter = range.first; iter != range.second; ++iter)
			{
				if (it->second.groupSize > iter->second.groupSize)
					it = iter;
			}

			if (nMaxSizePerGroup > 0 && (it->second.groupSize + getDataSize(tag.nTagType) > nMaxSizePerGroup))
			{
				TagInfoGroup newGrp;
				newGrp.tagVec.emplace_back(tag);
				newGrp.groupSize += getDataSize(tag.nDataType);

				devIt->second.insert({nScanIntv, newGrp});
				continue;
			}
			else
			{
				it->second.tagVec.emplace_back(tag);
				it->second.groupSize += getDataSize(tag.nDataType);
			}
		}
	}

	return ICV_SUCCESS;
}
