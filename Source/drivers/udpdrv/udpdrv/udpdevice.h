#ifndef UDPDEVICE_H
#define UDPDEVICE_H

#include "driversdk/cvdrivercommon.h"
#include <vector>
#include <map>
#include <mutex>
#include <string>
#include <thread>
#include <chrono>
#include <atomic>
#include "ace/INET_Addr.h"

#define		DEFAULT_RESPONSE_MAXLEN			65535
#define		DEFAULT_BUFFER_LEN		        4096

class CUDPDevice;

class CBuffer
{
public:
    CBuffer(size_t size);
    virtual ~CBuffer(void);

    // 写入数据接口
    int write(const void* buffer, int len);

    // 读取数据接口
    std::vector<unsigned char> read(void);

    // 读取的次数，如果为0，则说明缓冲区刚被刷新，如果大于0，则代表被读取的次数
	unsigned int getReadCount(){ return m_uiCount; }

private:
    // 数据存储缓冲区
    std::vector<unsigned char> m_buffer;
    // 互斥锁，保护并发访问
    std::mutex m_mutex;
    unsigned int m_uiCount;
};

struct PacketHeader 
{
    int16_t module_index;
    int16_t packet_length;
    int32_t packet_count;
};

class CBufferParser
{
public:
	CBufferParser(CUDPDevice* device);
	virtual ~CBufferParser(void);
	// 解析电文内容
	bool parser(std::vector<unsigned char> telegram);

private:
	//Big-Endian: 低地址存放高位
	class CBigParser
	{
		CUDPDevice* m_device;
	public:
		CBigParser(CUDPDevice* device):m_device(device){};

		bool parser(std::vector<unsigned char>& telegram);
	};
	//Little-Endian: 低地址存放低位
	class CLitParser
	{
		CUDPDevice* m_device;

	public:
		CLitParser(CUDPDevice* device):m_device(device){};

		bool parser(std::vector<unsigned char>& telegram);
	};

private:
	CBigParser m_bigParser;
	CLitParser m_litParser;
	CUDPDevice* m_device;
};

struct UDPDATABLOCK
{
    int moduleno;          // 模块序号
	int offset;            // 在数据报中的偏移
	DRVHANDLE hDataBlock;  // 数据块句柄
};

struct UDPMODULE
{
    int moduleno;       // 模块序号
    int byteOrder;      // 字节序
    int packageLen;     // 报文长度
};

class CUDPDevice
{
public:
    CUDPDevice(DRVHANDLE hDevice, CVDEVICE *pDevice);
    virtual ~CUDPDevice(void);

    long AddData(DRVHANDLE hDevice, DRVHANDLE hDataBlock);
    long read();

private:
    DRVHANDLE m_hDevice;		// 设备句柄
    bool m_bFlagComplete;	//此标记用来是否加载数据块完毕

    std::map<int, UDPDATABLOCK> m_addrMap;

    CBuffer* m_buffer;
    CBufferParser* m_parser;
    UDPMODULE m_module;
};

#endif