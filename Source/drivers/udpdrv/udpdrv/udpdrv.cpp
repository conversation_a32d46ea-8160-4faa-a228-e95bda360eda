// #include <memory.h>
// #include <map>
// #include "udpdrv.h"
// #include "common/cvcomm.hxx"
// #include "driversdk/cvdrivercommon.h"
// #include "common/TypeCast.h"
// #include <stdlib.h>
// #include <stdio.h>
// #include <string.h>
// #include <string>
// #include "ace/Default_Constants.h"
// #include "ace/DLL.h"
// #include "common/cvcomm.hxx"
// #include "gettext/libintl.h"
// #include "common/CommHelper.h"
// #include "common/CVLog.h"

// #define EC_ICV_INVALID_PARAMETER                    100

// CCVLog g_CVLogUDPDrv;
// const char g_szDriverName[ICV_DRIVERNAME_MAXLEN] = "udpdrv";

// std::map<DRVHANDLE, CUDPDevice*> g_mapDevices;
// clock_t interval_start_time = clock();

// CVDRIVER_EXPORTS long Begin()
// {
// 	g_CVLogUDPDrv.SetLogFileNameThread(g_szDriverName);
// 	return DRV_SUCCESS;
// }

// CVDRIVER_EXPORTS long Initialize(DRVHANDLE hDriver)
// {
// 	return DRV_SUCCESS;
// }

// CVDRIVER_EXPORTS long UnInitialize()
// {
// 	//释放所有的设备	
// 	for (map<DRVHANDLE, CUDPDevice*>::iterator iterDevice = g_mapDevices.begin();
// 		iterDevice != g_mapDevices.end(); iterDevice++)
// 	{
// 		delete iterDevice->second;
// 	}
// 	g_mapDevices.clear();

//     g_CVLogUDPDrv.StopLogThread();
//     return DRV_SUCCESS;
// }

// CVDRIVER_EXPORTS long OnReadData(DRVHANDLE hDevice, DRVHANDLE hDataBlock)
// {
// 	map<DRVHANDLE, CUDPDevice*>::iterator iterDevice = g_mapDevices.find(hDevice);
// 	if (iterDevice != g_mapDevices.end())
// 	{
// 		iterDevice->second->read();
// 	}
// 	return DRV_SUCCESS;
// }

// /* 不实现 */
// CVDRIVER_EXPORTS long OnWriteCmd(DRVHANDLE hDevice, DRVHANDLE hDatablock, int nTagByteOffset, int nTagBitOffset, char *szCmdDataBuff, int nCmdDataLenBits)
// {
// 	return DRV_SUCCESS;
// }

// CVDRIVER_EXPORTS long OnDataBlockAdd(DRVHANDLE hDevice, DRVHANDLE hDataBlock)
// {
// 	long lRet = DRV_SUCCESS;
// 	map<DRVHANDLE, CUDPDevice*>::iterator iterDevice = g_mapDevices.find(hDevice);
	
// 	if (iterDevice == g_mapDevices.end())
// 	{
// 		AddDevice(hDevice);
// 	}

// 	iterDevice = g_mapDevices.find(hDevice);
// 	if (iterDevice != g_mapDevices.end())
// 	{
// 		CUDPDevice *pUdpDevice = iterDevice->second;
// 		CVDATABLOCK *pDataBlock = Drv_GetDataBlockInfo(hDataBlock);
// 		if (!pDataBlock || !pDataBlock->pszAddress)
// 		{
// 			CV_ERROR(g_CVLogUDPDrv,-1, "failed to get datablock information");
// 			return EC_UDP_DATABLOCK_NOEXIST;
// 		}

// 		lRet = pUdpDevice->AddData(hDevice, hDataBlock);
//         //CVDEVICE* pDevice = Drv_GetDeviceInfo(hDevice);
//         //CV_INFO(pDataBlock, "device: %s parameters: %s add datablock: %s", pDevice->pszName, pDevice->pszConnParam, pDataBlock->pszName);
// 	}
// 	else
// 	{
// 		lRet = EC_UDP_DEVICE_NOEXIST;
// 	}

// 	return lRet;
// }

// //获取版本信息  
// CVDRIVER_EXPORTS long GetDrvFrameVersion()
// {
//     return 2;
// }

// const char * GetPureAddress(const char *pAddr)
// {
// 	static char szAddress[ICV_IOADDR_MAXLEN + 1];
// 	memset(szAddress, 0, sizeof(szAddress));
// 	const char *pTemp = strstr(pAddr, ":");
// 	if (pTemp != NULL)
// 	{
// 		const char *pTemp1 = strstr(pTemp + 1, "#");

// 		if (pTemp1 != NULL)
// 			memcpy(szAddress, pTemp + 1, pTemp1 - pTemp - 1);
// 		else
// 			strncpy(szAddress, pTemp + 1, ICV_IOADDR_MAXLEN);
// 	}
// 	return szAddress;
// }

// CVDRIVER_EXPORTS long TagsToGroups(const TagInfo *pDevTags, int nTagsNum,
// 	TagInfo *pOutDevTags, unsigned int *pnTagsNum, TagGroupInfo *pTagGrps, unsigned int *pnTagGrpsNum)
// {
// 	if (NULL == pOutDevTags || NULL == pnTagsNum || NULL == pTagGrps || NULL == pnTagGrpsNum)
// 		return EC_ICV_INVALID_PARAMETER;

// 	memset(pTagGrps, 0, *pnTagGrpsNum * sizeof(TagGroupInfo));

// 	*pnTagsNum = nTagsNum;
// 	*pnTagGrpsNum = 0;
// 	int nBlockNum = 0;

// 	std::map<string, string> mapDataBlockAddr;

// 	std::copy(pDevTags, pDevTags + nTagsNum, pOutDevTags);
	
// 	char szGroupName[ICV_DATABLOCKNAME_MAXLEN + 1];
// 	memset(szGroupName, 0, sizeof(szGroupName));

// 	for (int i = 0; i < nTagsNum; ++i)
// 	{
// 		string strAddress = GetPureAddress(pDevTags[i].szAddress);
// 		map<string, string>::iterator iter = mapDataBlockAddr.find(strAddress);
// 		if (iter != mapDataBlockAddr.end())
// 		{
// 			cvcommon::Safe_StrNCopy(pOutDevTags[i].szGrpName, iter->second.c_str(), ICV_DATABLOCKNAME_MAXLEN + 1);
// 			continue;
// 		}

// 		cvcommon::Safe_StrNCopy(pTagGrps[nBlockNum].szAddress, GetPureAddress(pDevTags[i].szAddress), ICV_IOADDR_MAXLEN);
// 		pTagGrps[nBlockNum].nElemBits = 8;
// 		pTagGrps[nBlockNum].nPollRate = 0;
// 		sprintf(pTagGrps[nBlockNum].szGroupName, "group%d", nBlockNum);
// 		sprintf(pOutDevTags[i].szGrpName, "group%d", nBlockNum);
// 		mapDataBlockAddr.insert(make_pair(pTagGrps[nBlockNum].szAddress, pTagGrps[nBlockNum].szGroupName));

// 		switch(pDevTags[i].nDataType)
// 		{
// 		case TAG_DATATYPE_BOOL:
// 			cvcommon::Safe_StrNCopy(pTagGrps[nBlockNum].szParam1, "Boolean", ICV_EXTEND_PARAM_LEN);
// 			pTagGrps[nBlockNum].nElemNum = 1;
// 			break;
// 			//无符号1字节
// 		case TAG_DATATYPE_BYTE:
// 			cvcommon::Safe_StrNCopy(pTagGrps[nBlockNum].szParam1, "Byte", ICV_EXTEND_PARAM_LEN);
// 			pTagGrps[nBlockNum].nElemNum = 1;
// 			break;
// 		case TAG_DATATYPE_INT:
// 			cvcommon::Safe_StrNCopy(pTagGrps[nBlockNum].szParam1, "INT", ICV_EXTEND_PARAM_LEN);
// 			pTagGrps[nBlockNum].nElemNum = 2;
// 			break;
// 		case TAG_DATATYPE_DINT:
// 			cvcommon::Safe_StrNCopy(pTagGrps[nBlockNum].szParam1, "DINT", ICV_EXTEND_PARAM_LEN);
// 			pTagGrps[nBlockNum].nElemNum = 4;
// 			break;
// 		case TAG_DATATYPE_WORD:
// 			cvcommon::Safe_StrNCopy(pTagGrps[nBlockNum].szParam1, "Word", ICV_EXTEND_PARAM_LEN);
// 			pTagGrps[nBlockNum].nElemNum = 2;
// 			break;
// 		case TAG_DATATYPE_DWORD:
// 			cvcommon::Safe_StrNCopy(pTagGrps[nBlockNum].szParam1, "DWord", ICV_EXTEND_PARAM_LEN);
// 			pTagGrps[nBlockNum].nElemNum = 4;
// 			break;
// 		case TAG_DATATYPE_REAL:
// 			cvcommon::Safe_StrNCopy(pTagGrps[nBlockNum].szParam1, "Float", ICV_EXTEND_PARAM_LEN);
// 			pTagGrps[nBlockNum].nElemNum = 4;
// 			break;
// 		case TAG_DATATYPE_USINT:
// 		case TAG_DATATYPE_CHAR:
// 		case TAG_DATATYPE_SINT:
// 		case TAG_DATATYPE_UINT:
// 		case TAG_DATATYPE_TIME:
// 		case TAG_DATATYPE_UDINT:
// 		case TAG_DATATYPE_LINT:
// 		case TAG_DATATYPE_LWORD:
// 		case TAG_DATATYPE_ULINT:
// 		case TAG_DATATYPE_LREAL:
// 		case TAG_DATATYPE_BLOB:
// 		case TAG_DATATYPE_STRING:
// 		default:
// 			break;
// 		}
// 		nBlockNum++;
// 	}
// 	*pnTagGrpsNum = nBlockNum;

//     return DRV_SUCCESS;
// }

// CVDRIVER_EXPORTS long OnDeviceAdd(DRVHANDLE hDevice)
// {
// 	AddDevice(hDevice);
// 	return DRV_SUCCESS;
// }
 
// CVDRIVER_EXPORTS long OnDeviceDelete(DRVHANDLE hDevice)
// {
// 	Drv_UpdateDevStatus(hDevice, DEV_STATUS_BAD);
// 	map<DRVHANDLE, CUDPDevice*>::iterator iterDevice = g_mapDevices.find(hDevice);
// 	CVDEVICE* pDevice = Drv_GetDeviceInfo(hDevice);
// 	if (iterDevice != g_mapDevices.end())
// 	{
// 		// iterDevice->second->disconnect(true);
//         CV_INFO(g_CVLogUDPDrv, "device: %s parameters: %s is deleted", pDevice->pszName, pDevice->pszConnParam);
// 		delete iterDevice->second;
// 		g_mapDevices.erase(iterDevice);
// 	}
// 	return DRV_SUCCESS;
// }

// CVDRIVER_EXPORTS long OnDataBlockTimer(DRVHANDLE hDevice, DRVHANDLE hDatablock)
// {
// 	return DRV_SUCCESS;
// }
 
// CVDRIVER_EXPORTS long OnDataBlockDelete(DRVHANDLE hDevice, DRVHANDLE hCfgDataBlock)
// {
// 	return DRV_SUCCESS;
// }

// // 添加设备
// long AddDevice(DRVHANDLE hDevice)
// {
// 	map<DRVHANDLE, CUDPDevice*>::iterator iterDevice = g_mapDevices.find(hDevice);
// 	if (iterDevice == g_mapDevices.end())
// 	{
// 		CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);
// 		if (!pDevice || !pDevice->pszConnParam)
// 		{
// 			CV_ERROR(g_CVLogUDPDrv,-1,"failed to get device information");
// 			return EC_UDP_DEVICE_NOEXIST;
// 		}

// 		CUDPDevice *pUDPDevice = new CUDPDevice(hDevice, pDevice);
// 		g_mapDevices.insert(make_pair(hDevice, pUDPDevice));
// 	}

// 	return DRV_SUCCESS;
// }

#include <stdlib.h>
#include <memory.h>
#include "ace/OS_NS_sys_time.h"
#include "udpdrv.h"
#include "math.h"

std::map<DRVHANDLE, std::map<DRVHANDLE, CVDATABLOCK*> > g_mapDataBlocks;
CCVLog g_CVLogUdp;
const char g_szDriverName[ICV_DRIVERNAME_MAXLEN] = "udpdrv";

CVDRIVER_EXPORTS long Begin()
{
	g_CVLogUdp.SetLogFileNameThread(g_szDriverName);
	return DRV_SUCCESS;
}

CVDRIVER_EXPORTS long Initialize(DRVHANDLE hDriver)
{
	return 0;
}

CVDRIVER_EXPORTS long UnInitialize()
{
    g_CVLogUdp.StopLogThread();
    return DRV_SUCCESS;
}

void CheckBlockStatus(DRVHANDLE hDevice, DRVHANDLE hDatablock, long lSuccess)
{
	if(lSuccess == DRV_SUCCESS)
		Drv_SetUserData(hDatablock, 0, 0);
	else
	{
		long lFailCountRecent = Drv_GetUserData(hDatablock, 0);
		if(lFailCountRecent > 3)	// 最近失败次数
		{
			Drv_UpdateBlockStatus(hDevice, hDatablock, DATA_STATUS_COMM_FAILURE);
			Drv_SetUserData(hDatablock, 0, 0); // 避免计数太大导致循环
		}
		else
			Drv_SetUserData(hDatablock, 0, lFailCountRecent + 1);
	}
}
CVDRIVER_EXPORTS long OnDataBlockTimer(DRVHANDLE hDevice, DRVHANDLE hDatablock)
{
	//Drv_ClearRecvBuffer(hDevice);
	CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);

	// 从设备接收消息
	long lCurRecvLen = 0;
	char szResponse[DEFAULT_RESPONSE_MAXLEN] = {0};

	unsigned short nPackageLen = 0;
	CV_TRACE(g_CVLogUdp, "Drv_RecvFromDevice Device %s  receive start ", pDevice->pszName);
	long lRecvBytes = Drv_RecvFromDevice(hDevice, szResponse + lCurRecvLen, sizeof(szResponse) - lCurRecvLen, pDevice->nRecvTimeout);
	CV_TRACE(g_CVLogUdp, "Drv_RecvFromDevice Device %s  receive end   lRecvBytes = %d ", pDevice->pszName, lRecvBytes);

	ACE_Time_Value* pLastRecvDataTime = (ACE_Time_Value*)Drv_GetUserDataPtr(hDevice, 1);
	ACE_Time_Value* pDisconnectTimeout = (ACE_Time_Value*)Drv_GetUserDataPtr(hDevice, 0);
	ACE_Time_Value tvCurrentTime = ACE_OS::gettimeofday();
	if(lRecvBytes <= 0)
	{
		//本次没有接收到数据，但是并没有到超时断开连接的时间
		if (tvCurrentTime - *pLastRecvDataTime < *pDisconnectTimeout)
		{
			return DRV_SUCCESS;
		}

		//接收超时，说明链路上一直没有数据，置该链路上的所有数据块质量为BAD
		for (std::map<DRVHANDLE, CVDATABLOCK*>::iterator itDB = g_mapDataBlocks[hDevice].begin(); itDB != g_mapDataBlocks[hDevice].end(); ++itDB)
		{
			Drv_UpdateBlockStatus(hDevice, itDB->first, DATA_STATUS_TIMEOUT_FAILURE);
		}

		CV_WARN(g_CVLogUdp, -1, "Disconnect with device %s because of receive timeout %d ms", pDevice->pszName, pDevice->nRecvTimeout);
		long lRet = Drv_DisconnectDevice(hDevice);
		CV_CHECK_FAIL_LOG(g_CVLogUdp, lRet, lRet, "Drv_DisconnectDevice");
		*pLastRecvDataTime = tvCurrentTime;
		Drv_SetUserDataPtr(hDevice, 1, pLastRecvDataTime);

		return DRV_SUCCESS;
	}
	else if (lRecvBytes <= 8)
	{
		//不合法的包长度
		CV_WARN(g_CVLogUdp, -1, "Device %s receive package of invalid size %d", pDevice->pszName, lRecvBytes);
		return DRV_SUCCESS;
	}

	CV_TRACE(g_CVLogUdp, "Drv_RecvFrom Device %s  receive %d bytes ", pDevice->pszName, lRecvBytes);
	// *pLastRecvDataTime = tvCurrentTime;
	// Drv_SetUserDataPtr(hDevice, 1, pLastRecvDataTime);

	//收到第一个包后解析出包长度字段以及电文号字段
	lCurRecvLen += lRecvBytes;
	unsigned short nTelID = 0;
	unsigned int nPackageCnt = 0;
	memcpy(&nTelID, szResponse, 2);
	memcpy(&nPackageLen, szResponse + 2, 2);
	memcpy(&nPackageCnt, szResponse + 4, 4);
	
	DRVHANDLE hTargetDB = NULL;
	for (std::map<DRVHANDLE, CVDATABLOCK*>::iterator itDB = g_mapDataBlocks[hDevice].begin(); itDB != g_mapDataBlocks[hDevice].end(); ++itDB)
	{
		if (nTelID == atoi(itDB->second->pszParam1))
		{
			//找到后如果还有后续包，继续收剩下的
			hTargetDB = itDB->first;
			// while (lCurRecvLen < nPackageLen)
			// {
			// 	lRecvBytes = Drv_RecvFromDevice(hDevice, szResponse + lCurRecvLen, sizeof(szResponse) - lCurRecvLen, pDevice->nRecvTimeout);
			// 	if (lRecvBytes <= 0)
			// 	{
			// 		Drv_UpdateBlockStatus(hDevice, itDB->first, DATA_STATUS_TIMEOUT_FAILURE);
			// 		CV_WARN(g_CVLogTdc, -1, "Device %s datablock %s receive timeout %d ms", pDevice->pszName, itDB->second->pszName, pDevice->nRecvTimeout);
			// 		return DRV_SUCCESS;
			// 	}
			// 	CV_TRACE(g_CVLogTdc, "Drv_RecvFrom Device %s  receive %d bytes total bytes is %d ", pDevice->pszName, lRecvBytes, lCurRecvLen);
			// 	lCurRecvLen += lRecvBytes;
			// }		
		}
	}

	if (hTargetDB)
	{
		//找到匹配的数据块，更新数据块
		CVDATABLOCK* pDatablock = Drv_GetDataBlockInfo(hTargetDB);
		int nSize = (pDatablock->nBlockDataSize > (nPackageLen - 8)) ? (nPackageLen - 8) : pDatablock->nBlockDataSize;
		Drv_LogMessage(DRV_LOGLEVEL_DEBUG, "Update device %s, datablock:%s, nSize:%d ", pDevice->pszName, pDatablock->pszName, nSize);
		Drv_UpdateBlockData(hDevice, hTargetDB, szResponse, 8, nSize, DATA_STATUS_OK, NULL);
	}
	else
	{
		//没有找到和电文号对应的数据块，丢弃该电文
		CV_DEBUG(g_CVLogUdp, "Can't find the corresponding datablock[PackageLen=%d, TelID=%d]", nPackageLen, nTelID);
	}
	
	//本次读取完成，将剩余的数据清空
	// long lRecvNoUseBytes = 0;
	// if (lRecvBytes == DEFAULT_RESPONSE_MAXLEN)
	// {
	// 	do
	// 	{
	// 		memset(szResponse, 0x00, sizeof(szResponse));
	// 		lRecvNoUseBytes = Drv_RecvFromDevice(hDevice, szResponse, sizeof(szResponse), 0);
	// 		Drv_LogMessage(DRV_LOGLEVEL_DEBUG, "Remove device %s message ", pDevice->pszName);
	// 	} while (lRecvNoUseBytes > 0);
	// }



	return DRV_SUCCESS;
}

/* 不实现 */
CVDRIVER_EXPORTS long OnWriteCmd(DRVHANDLE hDevice, DRVHANDLE hDatablock, int nTagByteOffset, int nTagBitOffset, char *szCmdDataBuff, int nCmdDataLenBits)
{
	return DRV_SUCCESS;
}

 /*  初始化数据块请求计数为0 .*/
CVDRIVER_EXPORTS long OnDataBlockAdd(DRVHANDLE hDevice, DRVHANDLE hDatablock)
{
	CVDEVICE* pDevice = Drv_GetDeviceInfo(hDevice);
	CVDATABLOCK *pDataBlock = Drv_GetDataBlockInfo(hDatablock);

	//初始化设置包请求失败计数为0
	CheckBlockStatus(hDevice, hDatablock, DRV_SUCCESS);

	//存储设备以及数据块配置信息
	std::map<DRVHANDLE, std::map<DRVHANDLE, CVDATABLOCK*> >::iterator it = g_mapDataBlocks.find(hDevice);
	if (it != g_mapDataBlocks.end())
	{
		it->second.insert(make_pair(hDatablock, pDataBlock));
	}
	else
	{
		std::map<DRVHANDLE, CVDATABLOCK*> mapDBs;
		mapDBs.insert(make_pair(hDatablock, pDataBlock));
		g_mapDataBlocks.insert(make_pair(hDevice, mapDBs));
	}
	
	return DRV_SUCCESS;
}

//获取版本信息  
CVDRIVER_EXPORTS long GetDrvFrameVersion()
{
    return 2;
}

CVDRIVER_EXPORTS long TagsToGroups(const TagInfo *pDevTags, int nTagsNum,
	TagInfo *pOutDevTags, unsigned int *pnTagsNum, TagGroupInfo *pTagGrps, unsigned int *pnTagGrpsNum)
{
	return DRV_SUCCESS;
}

CVDRIVER_EXPORTS long OnDeviceAdd(DRVHANDLE hDevice)
{
	// CVDEVICE* pDevice = Drv_GetDeviceInfo(hDevice);
    // long nReconnectCnt = atoi(pDevice->pszParam2);
	// if (nReconnectCnt <=0)
	// {
	// 	nReconnectCnt = 3;
	// }
	// ACE_Time_Value* pLastRecvDataTime = new ACE_Time_Value();
	// *pLastRecvDataTime = ACE_OS::gettimeofday();
	// ACE_Time_Value* pDisconnectTimeout = new ACE_Time_Value(0, nReconnectCnt*pDevice->nRecvTimeout*1000);
	// Drv_SetUserDataPtr(hDevice, 0, pDisconnectTimeout);
	// Drv_SetUserDataPtr(hDevice, 1, pLastRecvDataTime);

	// CV_INFO(g_CVLogUdp, "OnDeviceAdd %s reconnect cnt %d ", pDevice->pszName,nReconnectCnt);
	return DRV_SUCCESS;
}
 
CVDRIVER_EXPORTS long OnDeviceDelete(DRVHANDLE hDevice)
{
	return DRV_SUCCESS;
}
 
CVDRIVER_EXPORTS long OnDataBlockDelete(DRVHANDLE hDevice, DRVHANDLE hCfgDataBlock)
{
	return DRV_SUCCESS;
}

CVDRIVER_EXPORTS bool IsTagInBlock(char szAddr[TAG_TOTAL_SEGMENT][ICV_IOADDR_MAXLEN], CVDATABLOCK * pcvDataBlock, int32&nBitOffSet)
{
	bool bRet = false;
	char* pSegAddr = szAddr[SECOND_SEGMENT];
	char* pSegStart = pSegAddr;
	char* pSegEnd = pSegAddr;
	char* pSegBitOffset = szAddr[THIRD_SEGMENT];
	char szTemp[ICV_IOADDR_MAXLEN];
	int32 nPlatNo;

	nBitOffSet = 0;
	if(pcvDataBlock == NULL)
	{
		return false;
	}
	if(pcvDataBlock->pszAddress == NULL || pcvDataBlock->pszName == NULL)
	{
		return false;
	}
	memset(szTemp, 0, ICV_IOADDR_MAXLEN);

	if(strlen(szAddr[SECOND_SEGMENT]) != 0) //
	{
		while(*pSegEnd != '\0')
		{
			if(*pSegEnd == '@')
			{
				memset(szTemp, 0, ICV_IOADDR_MAXLEN);
				memcpy(szTemp, pSegStart, pSegEnd - pSegStart);
				nPlatNo = atoi(szTemp);
				pSegStart = pSegEnd + 1;
			}
			pSegEnd ++;
		}

		if(strcmp(pSegStart, pcvDataBlock->pszName) == 0)
		{
			char *pszDigitalOffset = szAddr[THIRD_SEGMENT];	// ��ַƫ��

			// ��ȡ��ַ�����ݿ��ڵ�Byteƫ���������ֻ�����ݿ����ƶ�û�������ƫ�ƣ���ȡ��ʼ��ַ
			if (szAddr[THIRD_SEGMENT][0] == '\0') // ֻ�����ݿ飬�����ֽ�
			{
				nBitOffSet = 0;
				bRet = true;
			}
			else // �����ֽں�λ��ƫ����
			{
				int nAbsAddressBits = BITS_PER_BYTE;
				bool bUseAbsAddress = false;
				int nByteCount = 1;	

				// ����������λΪ��λ��ƫ������
				if(bUseAbsAddress) // ʹ��B��b��W��DW����
				{
					nBitOffSet = ::atoi(pszDigitalOffset) * nAbsAddressBits;
				}
				else // ���ڿ��豸��dataBlockCfg.nStartWord ���ܲ��� 0 ������Ϊ0
				{
					// ��ȡ��ַ�����ݿ��ڵ�Byteƫ����
					int nTagAddress = ::atoi(pszDigitalOffset); // such as: 400003
					if(nTagAddress > 99999) // �������ĵ�ַֻ��Ϊ99999���ֽڡ���499999�����Ϊ99999
					{
						nTagAddress = nTagAddress - nTagAddress / 100000 * 100000;
						int nStartAddress = ::atoi(pcvDataBlock->pszAddress);
						if(nStartAddress > 99999) // �������ĵ�ַֻ��Ϊ99999���ֽڡ���499999�����Ϊ99999
							nStartAddress = nStartAddress - nStartAddress / 100000 * 100000;
						nBitOffSet = (nTagAddress - nStartAddress) *  nByteCount * pcvDataBlock->nElemBits;
					}
					else
						nBitOffSet = nTagAddress *  nByteCount * pcvDataBlock->nElemBits;
				}


				// ����������λΪ��λ��ƫ���������ڿ��豸��dataBlockCfg.nStartWord ���ܲ��� 0 ������Ϊ0
				if (szAddr[FOURTH_SEGMENT][0] != '\0') // ��������������ң��ź������ֽ��ڵ�ƫ�Ƶ�ַ
					nBitOffSet += ::atoi(szAddr[FOURTH_SEGMENT]);	// �������ð�ź����ƫ�ƣ���ҲҪ����

				if(nBitOffSet < 0)
				{
					// CV_WARN(g_CVDrvierCommonLog, -1, "TagAddr[%s %s %s %s] invalid bitoffset.",szAddr[DEVICE_SEGMENT], szAddr[SECOND_SEGMENT], szAddr[THIRD_SEGMENT], szAddr[FOURTH_SEGMENT]);
					return false;
				}

				// �����ֽں�λƫ��
				int32 nByteOffset = 0;
				nByteOffset = nBitOffSet / BITS_PER_BYTE;
				// dataBlockCfg.nWordBytes; // ������ÿ��Ԫ�ص�λ����ͨ��Ϊ8��Ҳ����Ϊ1����16��32
				int nDbDataLenInBytes = (int)pcvDataBlock->nBlockDataSize;	// 
				if (nByteOffset < 0 || nByteOffset-8 >= nDbDataLenInBytes)
				{
					// CV_WARN(g_CVDrvierCommonLog,-1,"TagAddr[%s %s %s %s] exceed blockLength %d.",szAddr[DEVICE_SEGMENT],szAddr[SECOND_SEGMENT],szAddr[THIRD_SEGMENT],szAddr[FOURTH_SEGMENT],nDbDataLenInBytes);
					return false;
				}
				bRet = true;
			}	
		}
	}
	return bRet;
}